FROM node:24-alpine AS base
LABEL authors="le<PERSON>"

FROM base AS deps
RUN apk update
# Check https://github.com/nodejs/docker-node/tree/b4117f9333da4138b03a546ec926ef50a31506c3#nodealpine to understand why libc6-compat might be needed.
RUN apk add --no-cache libc6-compat
#Set up pnpm
RUN corepack enable pnpm
# Set working directory
WORKDIR /app

COPY package.json pnpm-lock.yaml* .npmrc* ./

# Mount the .npmrc secret file to install dependencies securely without leaking credentials into image layers.
RUN --mount=type=secret,id=npmrc,target=/root/.npmrc pnpm i --frozen-lockfile

# Add lockfile and package.json's of isolated subworkspace
FROM base AS builder
WORKDIR /app

ARG BUILD_ENV
RUN echo $BUILD_ENV

#Set up pnpm
RUN corepack enable pnpm

COPY --from=deps /app/node_modules ./node_modules
COPY . .

COPY .env.$BUILD_ENV /app/.env.production

RUN pnpm build

FROM base AS runner
WORKDIR /app

ENV NODE_ENV=production

# Don't run production as root
RUN addgroup --system --gid 1001 nodejs
RUN adduser --system --uid 1001 nextjs
USER nextjs

# Automatically leverage output traces to reduce image size
# https://nextjs.org/docs/advanced-features/output-file-tracing
COPY --from=builder --chown=nextjs:nodejs /app/.next/standalone ./
COPY --from=builder --chown=nextjs:nodejs /app/.next/static ./.next/static
COPY --from=builder --chown=nextjs:nodejs /app/public ./public

EXPOSE 3005
ENV PORT=3005

CMD ["node", "server.js"]