import { vi, describe, it, expect, beforeEach } from 'vitest'
import { ApprovalRequestRouting } from '../../../src/app/approval-requests/RequestRouting'

// Mock the store actions
vi.mock('@/store/actions', () => ({
  getUserById: vi.fn().mockResolvedValue({}),
  getRoleById: vi.fn().mockResolvedValue({}),
}))

// Mock the store reducers
vi.mock('@/store/reducers', () => ({
  setSelectedApprovalRequest: vi.fn(),
}))

vi.mock('@dtbx/store/reducers', () => ({
  setSwitchToRoleDetails: vi.fn(),
}))

describe('ApprovalRequestRouting', () => {
  const mockDispatch = vi.fn()
  const mockRouter = {
    push: vi.fn(),
    back: vi.fn(),
    forward: vi.fn(),
    refresh: vi.fn(),
    replace: vi.fn(),
    prefetch: vi.fn(),
  }

  const createMockRequest = (module: string, type: string) => ({
    id: '123',
    makerCheckerType: {
      module,
      type,
      channel: 'web',
      checkerPermissions: [],
      makerPermissions: [],
      name: `${type}_${module}`,
      overridePermissions: [],
    },
    status: 'PENDING',
    createdBy: 'user1',
    createdAt: '2023-01-01',
    entityId: '123',
    maker: 'user1',
    dateCreated: '2023-01-01',
    dateModified: '2023-01-01',
    diff: [],
  })

  beforeEach(() => {
    vi.clearAllMocks()
  })

  it('routes CREATE_USER requests to the correct path', async () => {
    const mockRequest = createMockRequest('users', 'CREATE_USER')

    await ApprovalRequestRouting(mockRequest, mockDispatch, mockRouter)

    expect(mockRouter.push).toHaveBeenCalledWith('/users/details')
  })

  it('routes UPDATE_USER requests to the correct path', async () => {
    const mockRequest = createMockRequest('users', 'UPDATE_USER')

    await ApprovalRequestRouting(mockRequest, mockDispatch, mockRouter)

    expect(mockRouter.push).toHaveBeenCalledWith('/users/details')
  })

  it('routes CREATE_ROLE requests to the correct path', async () => {
    const mockRequest = createMockRequest('groups', 'CREATE_ROLE')

    await ApprovalRequestRouting(mockRequest, mockDispatch, mockRouter)

    expect(mockRouter.push).toHaveBeenCalledWith('/roles/details')
  })

  it('routes UPDATE_ROLE requests to the correct path', async () => {
    const mockRequest = createMockRequest('groups', 'UPDATE_ROLE')

    await ApprovalRequestRouting(mockRequest, mockDispatch, mockRouter)

    expect(mockRouter.push).toHaveBeenCalledWith('/roles/details')
  })

  it('routes DELETE_ROLE requests to the correct path', async () => {
    const mockRequest = createMockRequest('groups', 'DELETE_ROLE')

    await ApprovalRequestRouting(mockRequest, mockDispatch, mockRouter)

    expect(mockRouter.push).toHaveBeenCalledWith('/roles/details')
  })

  it('routes CREATE_CUSTOMER requests to the correct path', async () => {
    const mockRequest = createMockRequest('Customers', 'CREATE_CUSTOMER')

    await ApprovalRequestRouting(mockRequest, mockDispatch, mockRouter)

    expect(mockRouter.push).not.toHaveBeenCalled()
  })

  it('routes UPDATE_CUSTOMER requests to the correct path', async () => {
    const mockRequest = createMockRequest('Customers', 'UPDATE_CUSTOMER')

    await ApprovalRequestRouting(mockRequest, mockDispatch, mockRouter)
    expect(mockRouter.push).not.toHaveBeenCalled()
  })

  it('handles unknown request types gracefully', async () => {
    const mockRequest = createMockRequest('Unknown', 'UNKNOWN_TYPE')

    await ApprovalRequestRouting(mockRequest, mockDispatch, mockRouter)
    expect(mockRouter.push).not.toHaveBeenCalled()
  })
})
