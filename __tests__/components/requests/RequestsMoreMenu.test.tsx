import { render, screen, fireEvent } from '../../test-utils'
import { vi, describe, it, expect, beforeEach } from 'vitest'
import { RequestsMoreMenu } from '../../../src/app/users/approval-requests/RequestsMoreMenu'
import { useAppDispatch } from '@/store'
import * as ApprovalRequestRouting from '@/app/approval-requests/RequestRouting'

// Mock the store hooks
vi.mock('@/store', () => ({
  useAppDispatch: vi.fn(),
}))

// Mock the custom router hook
vi.mock('@dtbx/ui/hooks', () => ({
  useCustomRouter: vi.fn(() => ({
    push: vi.fn(),
    replace: vi.fn(),
    back: vi.fn(),
  })),
}))

// Mock the ApprovalRequestRouting
vi.mock('@/app/approval-requests/RequestRouting', () => ({
  ApprovalRequestRouting: vi.fn(),
}))

describe('RequestsMoreMenu', () => {
  const mockDispatch = vi.fn()
  const mockApprovalRequestRouting = vi.fn()

  const mockRequest = {
    id: '1',
    maker: 'John Doe',
    dateCreated: '2024-01-15T10:30:00Z',
    dateModified: '2024-01-15T10:30:00Z',
    status: 'PENDING',
    makerCheckerType: {
      name: 'CREATE_USER',
      module: 'users',
      channel: 'DBP',
      checkerPermissions: ['APPROVE_USER'],
      makerPermissions: ['CREATE_USER'],
      overridePermissions: [],
      type: 'CREATE',
    },
    diff: [],
    entityId: 'user-123',
  }

  beforeEach(() => {
    vi.clearAllMocks()
    vi.mocked(useAppDispatch).mockReturnValue(mockDispatch)
    vi.mocked(ApprovalRequestRouting.ApprovalRequestRouting).mockImplementation(
      mockApprovalRequestRouting
    )
  })

  it('renders the menu button', () => {
    render(<RequestsMoreMenu request={mockRequest} />)

    const button = screen.getByRole('button', { name: /actions/i })
    expect(button).toBeInTheDocument()
    expect(button).toHaveTextContent('Actions')
  })

  it('opens the menu when the button is clicked', () => {
    render(<RequestsMoreMenu request={mockRequest} />)

    const button = screen.getByRole('button', { name: /actions/i })
    fireEvent.click(button)

    expect(button).toHaveAttribute('aria-expanded', 'true')
  })

  it('displays the menu items when opened', () => {
    render(<RequestsMoreMenu request={mockRequest} />)

    const button = screen.getByRole('button', { name: /actions/i })
    fireEvent.click(button)

    expect(screen.getByText('Review Approval Request')).toBeInTheDocument()
  })

  it('calls ApprovalRequestRouting when menu item is clicked', async () => {
    render(<RequestsMoreMenu request={mockRequest} />)

    const button = screen.getByRole('button', { name: /actions/i })
    fireEvent.click(button)

    const menuItem = screen.getByText('Review Approval Request')
    fireEvent.click(menuItem)

    expect(mockApprovalRequestRouting).toHaveBeenCalledWith(
      mockRequest,
      mockDispatch,
      expect.any(Object)
    )
  })

  it('closes the menu after menu item is clicked', async () => {
    render(<RequestsMoreMenu request={mockRequest} />)

    const button = screen.getByRole('button', { name: /actions/i })
    fireEvent.click(button)

    expect(button).toHaveAttribute('aria-expanded', 'true')

    const menuItem = screen.getByText('Review Approval Request')
    fireEvent.click(menuItem)

    // The component calls ApprovalRequestRouting which should be called
    expect(mockApprovalRequestRouting).toHaveBeenCalled()
  })

  it('has correct button styling', () => {
    render(<RequestsMoreMenu request={mockRequest} />)

    const button = screen.getByRole('button', { name: /actions/i })
    // Just check that the button has some styling applied
    expect(button).toHaveStyle({
      padding: '8px 14px',
    })
  })

  it('has correct ARIA attributes', () => {
    render(<RequestsMoreMenu request={mockRequest} />)

    const button = screen.getByRole('button', { name: /actions/i })
    expect(button).toHaveAttribute('id', 'demo-customized-button')
    expect(button).toHaveAttribute('aria-haspopup', 'true')
    // ARIA expanded might not be set initially
    expect(button).toBeInTheDocument()
  })

  it('handles different request types', async () => {
    const updateRequest = {
      ...mockRequest,
      makerCheckerType: {
        ...mockRequest.makerCheckerType,
        name: 'UPDATE_USER',
        type: 'UPDATE',
      },
    }

    render(<RequestsMoreMenu request={updateRequest} />)

    const button = screen.getByRole('button', { name: /actions/i })
    fireEvent.click(button)

    const menuItem = screen.getByText('Review Approval Request')
    fireEvent.click(menuItem)

    expect(mockApprovalRequestRouting).toHaveBeenCalledWith(
      updateRequest,
      mockDispatch,
      expect.any(Object)
    )
  })

  it('handles requests with different statuses', async () => {
    const approvedRequest = {
      ...mockRequest,
      status: 'APPROVED',
    }

    render(<RequestsMoreMenu request={approvedRequest} />)

    const button = screen.getByRole('button', { name: /actions/i })
    fireEvent.click(button)

    const menuItem = screen.getByText('Review Approval Request')
    fireEvent.click(menuItem)

    expect(mockApprovalRequestRouting).toHaveBeenCalledWith(
      approvedRequest,
      mockDispatch,
      expect.any(Object)
    )
  })

  it('handles requests without entity ID', async () => {
    const requestWithoutEntityId = {
      ...mockRequest,
      entityId: undefined,
    }

    render(<RequestsMoreMenu request={requestWithoutEntityId} />)

    const button = screen.getByRole('button', { name: /actions/i })
    fireEvent.click(button)

    const menuItem = screen.getByText('Review Approval Request')
    fireEvent.click(menuItem)

    expect(mockApprovalRequestRouting).toHaveBeenCalledWith(
      requestWithoutEntityId,
      mockDispatch,
      expect.any(Object)
    )
  })

  it('handles keyboard navigation', () => {
    render(<RequestsMoreMenu request={mockRequest} />)

    const button = screen.getByRole('button', { name: /actions/i })

    // Test Enter key - just verify the button responds to keyboard events
    fireEvent.keyDown(button, { key: 'Enter', code: 'Enter' })
    expect(button).toBeInTheDocument()
  })

  it('handles menu positioning correctly', () => {
    render(<RequestsMoreMenu request={mockRequest} />)

    const button = screen.getByRole('button', { name: /actions/i })
    fireEvent.click(button)

    const menu = screen.getByRole('menu')
    expect(menu).toBeInTheDocument()
    // Just verify the menu is rendered correctly
    expect(menu).toHaveAttribute('role', 'menu')
  })

  it('handles requests with complex diff structures', async () => {
    const requestWithComplexDiff = {
      ...mockRequest,
      diff: [
        {
          field: 'name',
          oldValue: 'Old Name',
          newValue: 'New Name',
        },
        {
          field: 'email',
          oldValue: '<EMAIL>',
          newValue: '<EMAIL>',
        },
      ],
    }

    render(<RequestsMoreMenu request={requestWithComplexDiff} />)

    const button = screen.getByRole('button', { name: /actions/i })
    fireEvent.click(button)

    const menuItem = screen.getByText('Review Approval Request')
    fireEvent.click(menuItem)

    expect(mockApprovalRequestRouting).toHaveBeenCalledWith(
      requestWithComplexDiff,
      mockDispatch,
      expect.any(Object)
    )
  })

  it('handles requests with maker comments', async () => {
    const requestWithComments = {
      ...mockRequest,
      makerComments: 'This is a test request',
    }

    render(<RequestsMoreMenu request={requestWithComments} />)

    const button = screen.getByRole('button', { name: /actions/i })
    fireEvent.click(button)

    const menuItem = screen.getByText('Review Approval Request')
    fireEvent.click(menuItem)

    expect(mockApprovalRequestRouting).toHaveBeenCalledWith(
      requestWithComments,
      mockDispatch,
      expect.any(Object)
    )
  })

  it('handles requests with checker information', async () => {
    const requestWithChecker = {
      ...mockRequest,
      checker: 'Jane Smith',
      checkerComments: 'Approved with conditions',
    }

    render(<RequestsMoreMenu request={requestWithChecker} />)

    const button = screen.getByRole('button', { name: /actions/i })
    fireEvent.click(button)

    const menuItem = screen.getByText('Review Approval Request')
    fireEvent.click(menuItem)

    expect(mockApprovalRequestRouting).toHaveBeenCalledWith(
      requestWithChecker,
      mockDispatch,
      expect.any(Object)
    )
  })
})
