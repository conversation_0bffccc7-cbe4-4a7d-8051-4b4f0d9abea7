import { render, screen, fireEvent } from '../../test-utils'
import { vi, describe, it, expect, beforeEach } from 'vitest'
import { RightsPageHeader } from '../../../src/app/rights/PageHeader'
import { useAppDispatch, useAppSelector } from '@/store'
import * as rolesActions from '@/store/actions/roles'

// Mock the store hooks
vi.mock('@/store', () => ({
  useAppDispatch: vi.fn(),
  useAppSelector: vi.fn(),
}))

// Mock the actions
vi.mock('@/store/actions/roles', () => ({
  getPermissionsGroup: vi.fn(),
}))

// Mock the CustomFilterBox component
vi.mock('@/app/approval-requests/CustomFilterBox', () => ({
  CustomFilterBox: ({
    openFilter,
    setOpenFilter,
    searchValue,
    handleSearch,
    filters,
    onFilterChange,
  }: any) => (
    <div data-testid="custom-filter-box">
      <input
        data-testid="search-input"
        value={searchValue}
        onChange={handleSearch}
        placeholder="Search permissions"
      />
      <button
        data-testid="filter-toggle"
        onClick={() => setOpenFilter(!openFilter)}
      >
        {openFilter ? 'Close Filter' : 'Open Filter'}
      </button>
      <div data-testid="filters-container">
        {filters.map((filter: any, index: number) => (
          <div key={index} data-testid={`filter-${filter.filterName}`}>
            <span>{filter.filterName}</span>
            <select
              data-testid={`filter-select-${filter.filterName}`}
              onChange={(e) =>
                onFilterChange({ [filter.filterName]: e.target.value })
              }
            >
              <option value="">Select {filter.filterName}</option>
              {filter.options.map((option: any) => (
                <option key={option.key} value={option.value}>
                  {option.label}
                </option>
              ))}
            </select>
          </div>
        ))}
      </div>
      <button
        data-testid="apply-empty-filter"
        onClick={() => onFilterChange({})}
      >
        Clear Filters
      </button>
    </div>
  ),
}))

vi.mock('tiny-case', () => ({
  sentenceCase: (str: string) =>
    str.charAt(0).toUpperCase() + str.slice(1).toLowerCase(),
}))

describe('RightsPageHeader', () => {
  const mockDispatch = vi.fn()
  const mockGetPermissionsGroup = vi.fn()
  const mockSearch = vi.fn()
  const mockFilter = vi.fn()

  const mockPermissionGroups = [
    { name: 'users', description: 'User management' },
    { name: 'roles', description: 'Role management' },
    { name: 'permissions', description: 'Permission management' },
  ]

  const mockState = {
    permissionGroup: {
      data: mockPermissionGroups,
      pageNumber: 1,
      pageSize: 10,
      totalNumberOfPages: 1,
      totalElements: 3,
    },
  }

  beforeEach(() => {
    vi.clearAllMocks()
    vi.mocked(useAppDispatch).mockReturnValue(mockDispatch)
    vi.mocked(useAppSelector).mockReturnValue(mockState)
    vi.mocked(rolesActions.getPermissionsGroup).mockImplementation(
      mockGetPermissionsGroup
    )
  })

  it('renders the page header with filter box', () => {
    render(<RightsPageHeader search={mockSearch} filter={mockFilter} />)

    expect(screen.getByTestId('custom-filter-box')).toBeInTheDocument()
    expect(screen.getByTestId('search-input')).toBeInTheDocument()
    expect(screen.getByTestId('filter-toggle')).toBeInTheDocument()
  })

  it('handles search input changes', () => {
    render(<RightsPageHeader search={mockSearch} filter={mockFilter} />)

    const searchInput = screen.getByTestId('search-input')
    fireEvent.change(searchInput, { target: { value: 'CREATE_USER' } })

    expect(mockSearch).toHaveBeenCalledWith('CREATE_USER')
    expect(searchInput).toHaveValue('CREATE_USER')
  })

  it('handles filter toggle', () => {
    render(<RightsPageHeader search={mockSearch} filter={mockFilter} />)

    const filterToggle = screen.getByTestId('filter-toggle')
    expect(filterToggle).toHaveTextContent('Open Filter')

    fireEvent.click(filterToggle)
    expect(filterToggle).toHaveTextContent('Close Filter')
  })

  it('renders visibility filter options', () => {
    render(<RightsPageHeader search={mockSearch} filter={mockFilter} />)

    const visibilityFilter = screen.getByTestId('filter-Is Visible')
    expect(visibilityFilter).toBeInTheDocument()
    expect(screen.getByText('Is Visible')).toBeInTheDocument()

    const visibilitySelect = screen.getByTestId('filter-select-Is Visible')
    expect(visibilitySelect).toBeInTheDocument()
  })

  it('renders module filter options', () => {
    render(<RightsPageHeader search={mockSearch} filter={mockFilter} />)

    const moduleFilter = screen.getByTestId('filter-Module')
    expect(moduleFilter).toBeInTheDocument()
    expect(screen.getByText('Module')).toBeInTheDocument()

    const moduleSelect = screen.getByTestId('filter-select-Module')
    expect(moduleSelect).toBeInTheDocument()
  })

  it('handles visibility filter changes', () => {
    render(<RightsPageHeader search={mockSearch} filter={mockFilter} />)

    const visibilitySelect = screen.getByTestId('filter-select-Is Visible')
    fireEvent.change(visibilitySelect, { target: { value: 'yes' } })

    expect(mockFilter).toHaveBeenCalledWith({ 'Is Visible': 'yes' })
  })

  it('handles module filter changes', () => {
    render(<RightsPageHeader search={mockSearch} filter={mockFilter} />)

    const moduleSelect = screen.getByTestId('filter-select-Module')
    fireEvent.change(moduleSelect, { target: { value: 'users' } })

    expect(mockFilter).toHaveBeenCalledWith({ Module: 'users' })
  })

  it('calls filter with empty object on mount', () => {
    render(<RightsPageHeader search={mockSearch} filter={mockFilter} />)

    expect(mockFilter).toHaveBeenCalledWith({})
  })

  it('handles clearing filters', () => {
    render(<RightsPageHeader search={mockSearch} filter={mockFilter} />)

    const clearFiltersButton = screen.getByTestId('apply-empty-filter')
    fireEvent.click(clearFiltersButton)

    expect(mockFilter).toHaveBeenCalledWith({})
  })

  it('displays correct visibility filter options', () => {
    render(<RightsPageHeader search={mockSearch} filter={mockFilter} />)

    const visibilitySelect = screen.getByTestId('filter-select-Is Visible')

    // Check that Yes and No options are available
    expect(visibilitySelect).toBeInTheDocument()
    // The options are rendered by the mock, so we just verify the select exists
  })

  it('displays correct module filter options based on permission groups', () => {
    render(<RightsPageHeader search={mockSearch} filter={mockFilter} />)

    const moduleSelect = screen.getByTestId('filter-select-Module')
    expect(moduleSelect).toBeInTheDocument()

    // The module filter should be populated with permission groups
    const moduleFilter = screen.getByTestId('filter-Module')
    expect(moduleFilter).toBeInTheDocument()
  })

  it('handles empty permission groups', () => {
    vi.mocked(useAppSelector).mockReturnValue({
      permissionGroup: {
        data: null,
        pageNumber: 1,
        pageSize: 10,
        totalNumberOfPages: 0,
        totalElements: 0,
      },
    })

    render(<RightsPageHeader search={mockSearch} filter={mockFilter} />)

    expect(screen.getByTestId('custom-filter-box')).toBeInTheDocument()
    expect(screen.getByTestId('filter-Module')).toBeInTheDocument()
  })

  it('handles undefined permission groups', () => {
    vi.mocked(useAppSelector).mockReturnValue({
      permissionGroup: {
        data: undefined,
        pageNumber: 1,
        pageSize: 10,
        totalNumberOfPages: 0,
        totalElements: 0,
      },
    })

    render(<RightsPageHeader search={mockSearch} filter={mockFilter} />)

    expect(screen.getByTestId('custom-filter-box')).toBeInTheDocument()
    expect(screen.getByTestId('filter-Module')).toBeInTheDocument()
  })

  it('applies sentence case to module names', () => {
    render(<RightsPageHeader search={mockSearch} filter={mockFilter} />)

    // The sentence case transformation is handled by the mock
    // We just verify that the module filter is rendered
    expect(screen.getByTestId('filter-Module')).toBeInTheDocument()
  })

  it('maintains search value state correctly', () => {
    render(<RightsPageHeader search={mockSearch} filter={mockFilter} />)

    const searchInput = screen.getByTestId('search-input')

    fireEvent.change(searchInput, { target: { value: 'test' } })
    expect(searchInput).toHaveValue('test')

    fireEvent.change(searchInput, { target: { value: 'another test' } })
    expect(searchInput).toHaveValue('another test')
  })

  it('handles multiple filter changes', () => {
    render(<RightsPageHeader search={mockSearch} filter={mockFilter} />)

    const visibilitySelect = screen.getByTestId('filter-select-Is Visible')
    const moduleSelect = screen.getByTestId('filter-select-Module')

    fireEvent.change(visibilitySelect, { target: { value: 'yes' } })
    fireEvent.change(moduleSelect, { target: { value: 'users' } })

    expect(mockFilter).toHaveBeenCalledWith({ 'Is Visible': 'yes' })
    expect(mockFilter).toHaveBeenCalledWith({ Module: 'users' })
  })

  it('handles empty search input', () => {
    render(<RightsPageHeader search={mockSearch} filter={mockFilter} />)

    const searchInput = screen.getByTestId('search-input')
    fireEvent.change(searchInput, { target: { value: '' } })

    // The search input should be updated
    expect(searchInput).toHaveValue('')
  })

  it('renders with correct stack styling', () => {
    render(<RightsPageHeader search={mockSearch} filter={mockFilter} />)

    const stackElement = screen.getByTestId('custom-filter-box').parentElement
    expect(stackElement).toHaveStyle({
      justifyContent: 'space-between',
    })
  })

  it('handles filter type configurations correctly', () => {
    render(<RightsPageHeader search={mockSearch} filter={mockFilter} />)

    // Both filters should be dropdown/single type
    expect(screen.getByTestId('filter-Is Visible')).toBeInTheDocument()
    expect(screen.getByTestId('filter-Module')).toBeInTheDocument()
  })

  it('handles search with special characters', () => {
    render(<RightsPageHeader search={mockSearch} filter={mockFilter} />)

    const searchInput = screen.getByTestId('search-input')
    fireEvent.change(searchInput, { target: { value: 'CREATE_USER@#$' } })

    expect(mockSearch).toHaveBeenCalledWith('CREATE_USER@#$')
  })

  it('handles rapid search input changes', () => {
    render(<RightsPageHeader search={mockSearch} filter={mockFilter} />)

    const searchInput = screen.getByTestId('search-input')

    fireEvent.change(searchInput, { target: { value: 'C' } })
    fireEvent.change(searchInput, { target: { value: 'CR' } })
    fireEvent.change(searchInput, { target: { value: 'CRE' } })

    expect(mockSearch).toHaveBeenCalledTimes(3)
    expect(mockSearch).toHaveBeenLastCalledWith('CRE')
  })
})
