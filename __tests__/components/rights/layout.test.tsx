import { render, screen } from '../../test-utils'
import { vi, describe, it, expect } from 'vitest'
import Layout from '../../../src/app/rights/layout'

// Mock the UI components
vi.mock('@dtbx/ui/icons', () => ({
  RightsIcon: ({ width, height }: any) => (
    <div data-testid="rights-icon" data-width={width} data-height={height}>
      Rights Icon
    </div>
  ),
}))

describe('Rights Layout', () => {
  it('renders the layout with rights icon and title', () => {
    render(<Layout>Test Content</Layout>)

    expect(screen.getByTestId('rights-icon')).toBeInTheDocument()
    expect(screen.getByText('Rights Management')).toBeInTheDocument()
  })

  it('displays the rights icon with correct dimensions', () => {
    render(<Layout>Test Content</Layout>)

    const rightsIcon = screen.getByTestId('rights-icon')
    expect(rightsIcon).toHaveAttribute('data-width', '28')
    expect(rightsIcon).toHaveAttribute('data-height', '26')
  })

  it('renders the title with correct typography variant', () => {
    render(<Layout>Test Content</Layout>)

    const title = screen.getByText('Rights Management')
    expect(title).toBeInTheDocument()
    // The Typography component is mocked, so we just verify the text is present
  })

  it('renders children content', () => {
    const testContent = 'This is test content for the layout'
    render(<Layout>{testContent}</Layout>)

    expect(screen.getByText(testContent)).toBeInTheDocument()
  })

  it('renders divider between header and content', () => {
    render(<Layout>Test Content</Layout>)

    // The Divider component from MUI should be rendered
    // Since it's not mocked, it should render as a <hr> element
    const divider = document.querySelector('hr')
    expect(divider).toBeInTheDocument()
  })

  it('renders with correct layout structure', () => {
    render(<Layout>Test Content</Layout>)

    // Check that the main stack container exists
    const mainStack = screen.getByText('Rights Management').closest('div')
    expect(mainStack).toBeInTheDocument()

    // Check that the header stack with icon and title exists
    const headerStack = screen.getByTestId('rights-icon').parentElement
    expect(headerStack).toBeInTheDocument()
    expect(headerStack).toContainElement(screen.getByTestId('rights-icon'))
    expect(headerStack).toContainElement(screen.getByText('Rights Management'))
  })

  it('handles empty children', () => {
    render(<Layout>{''}</Layout>)

    expect(screen.getByTestId('rights-icon')).toBeInTheDocument()
    expect(screen.getByText('Rights Management')).toBeInTheDocument()
  })

  it('handles multiple children elements', () => {
    render(
      <Layout>
        <div>First child</div>
        <div>Second child</div>
        <span>Third child</span>
      </Layout>
    )

    expect(screen.getByText('First child')).toBeInTheDocument()
    expect(screen.getByText('Second child')).toBeInTheDocument()
    expect(screen.getByText('Third child')).toBeInTheDocument()
  })

  it('handles complex children content', () => {
    render(
      <Layout>
        <div>
          <h1>Page Title</h1>
          <p>Page description</p>
          <button>Action Button</button>
        </div>
      </Layout>
    )

    expect(screen.getByText('Page Title')).toBeInTheDocument()
    expect(screen.getByText('Page description')).toBeInTheDocument()
    expect(screen.getByText('Action Button')).toBeInTheDocument()
  })

  it('maintains proper component hierarchy', () => {
    render(<Layout>Test Content</Layout>)

    // The layout should have a main Stack container
    const mainContainer = screen.getByText('Rights Management').closest('div')
    expect(mainContainer).toBeInTheDocument()

    // The header should be within the main container
    const headerContainer = screen.getByTestId('rights-icon').parentElement
    expect(headerContainer).toBeInTheDocument()

    // The content should be rendered
    const content = screen.getByText('Test Content')
    expect(content).toBeInTheDocument()
  })

  it('applies correct styling to header stack', () => {
    render(<Layout>Test Content</Layout>)

    const headerStack = screen.getByTestId('rights-icon').parentElement
    expect(headerStack).toHaveStyle({
      flexDirection: 'row',
    })
  })

  it('renders consistently with different content types', () => {
    const { rerender } = render(<Layout>String content</Layout>)
    expect(screen.getByText('String content')).toBeInTheDocument()

    rerender(
      <Layout>
        <div>JSX content</div>
      </Layout>
    )
    expect(screen.getByText('JSX content')).toBeInTheDocument()

    rerender(<Layout>{123}</Layout>)
    expect(screen.getByText('123')).toBeInTheDocument()
  })

  it('handles null children gracefully', () => {
    render(<Layout>{null}</Layout>)

    expect(screen.getByTestId('rights-icon')).toBeInTheDocument()
    expect(screen.getByText('Rights Management')).toBeInTheDocument()
  })

  it('handles undefined children gracefully', () => {
    render(<Layout>{undefined}</Layout>)

    expect(screen.getByTestId('rights-icon')).toBeInTheDocument()
    expect(screen.getByText('Rights Management')).toBeInTheDocument()
  })

  it('renders with proper accessibility structure', () => {
    render(<Layout>Test Content</Layout>)

    // The title should be rendered as a heading
    const title = screen.getByText('Rights Management')
    expect(title).toBeInTheDocument()

    // The icon should be present and accessible
    const icon = screen.getByTestId('rights-icon')
    expect(icon).toBeInTheDocument()
  })

  it('maintains layout integrity with dynamic content', () => {
    const { rerender } = render(<Layout>Initial content</Layout>)

    expect(screen.getByText('Initial content')).toBeInTheDocument()
    expect(screen.getByTestId('rights-icon')).toBeInTheDocument()
    expect(screen.getByText('Rights Management')).toBeInTheDocument()

    rerender(<Layout>Updated content with more text and elements</Layout>)

    expect(
      screen.getByText('Updated content with more text and elements')
    ).toBeInTheDocument()
    expect(screen.getByTestId('rights-icon')).toBeInTheDocument()
    expect(screen.getByText('Rights Management')).toBeInTheDocument()
  })

  it('handles special characters in children', () => {
    const specialContent =
      'Content with special chars: @#$%^&*()_+-=[]{}|;:,.<>?'
    render(<Layout>{specialContent}</Layout>)

    expect(screen.getByText(specialContent)).toBeInTheDocument()
    expect(screen.getByTestId('rights-icon')).toBeInTheDocument()
    expect(screen.getByText('Rights Management')).toBeInTheDocument()
  })

  it('renders correctly with nested components', () => {
    render(
      <Layout>
        <div>
          <header>
            <h1>Nested Header</h1>
          </header>
          <main>
            <section>
              <p>Nested content</p>
            </section>
          </main>
        </div>
      </Layout>
    )

    expect(screen.getByText('Nested Header')).toBeInTheDocument()
    expect(screen.getByText('Nested content')).toBeInTheDocument()
    expect(screen.getByTestId('rights-icon')).toBeInTheDocument()
    expect(screen.getByText('Rights Management')).toBeInTheDocument()
  })
})
