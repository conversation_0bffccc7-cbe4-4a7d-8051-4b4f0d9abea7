import { render, screen, fireEvent, waitFor } from '../../test-utils'
import { vi, describe, it, expect, beforeEach } from 'vitest'
import { useState } from 'react'
import { CreateRole } from '../../../src/app/roles/CreateRole'
import { useAppDispatch, useAppSelector } from '@/store'
import * as rolesActions from '@/store/actions'

// Mock the store hooks
vi.mock('@/store', () => ({
  useAppDispatch: vi.fn(),
  useAppSelector: vi.fn(),
}))

// Mock the actions
vi.mock('@/store/actions', () => ({
  createRole: vi.fn(),
  makeCreateRole: vi.fn(),
}))

// Mock the access control utilities
vi.mock('@dtbx/store/utils', () => ({
  ACCESS_CONTROLS: {
    CREATE_ROLES: 'CREATE_ROLES',
  },
  AccessControlWrapper: ({ children }: any) => <div>{children}</div>,
  HasAccessToRights: vi.fn(() => true),
}))

// Mock the UI components
vi.mock('@dtbx/ui/components/CheckBox', () => ({
  CustomCheckBox: ({ checked, onChange }: any) => (
    <input
      type="checkbox"
      checked={checked}
      onChange={onChange}
      data-testid="custom-checkbox"
    />
  ),
}))

vi.mock('@dtbx/ui/components', () => ({
  LoadingButton: () => <div data-testid="loading-button">Loading...</div>,
}))

// Mock the CreateRoleResponse components
vi.mock('../../../src/app/roles/CreateRoleResponse', () => ({
  UserRoleCreateSuccess: ({ handleCloseDrawer }: any) => (
    <div data-testid="create-success">
      <button onClick={handleCloseDrawer}>Close Success</button>
    </div>
  ),
  UserRoleCreateFailure: () => (
    <div data-testid="create-failure">Create Failed</div>
  ),
}))

// Mock Formik
vi.mock('formik', () => ({
  useFormik: vi.fn(() => ({
    values: {
      name: '',
      description: '',
      Permissions: [],
    },
    errors: {},
    touched: {},
    getFieldProps: vi.fn(() => ({
      name: 'test',
      value: 'test',
      onChange: vi.fn(),
      onBlur: vi.fn(),
    })),
    handleSubmit: vi.fn(),
    setFieldValue: vi.fn(),
  })),
  Form: ({ children }: any) => (
    <form data-testid="create-role-form">{children}</form>
  ),
  FormikProvider: ({ children }: any) => <div>{children}</div>,
}))

// Mock Yup validation
vi.mock('yup', () => ({
  object: vi.fn(() => ({
    shape: vi.fn(() => ({})),
  })),
  string: vi.fn(() => ({
    required: vi.fn(() => ({})),
  })),
  array: vi.fn(() => ({
    min: vi.fn(() => ({})),
  })),
}))

vi.mock('tiny-case', () => ({
  sentenceCase: (str: string) =>
    str.charAt(0).toUpperCase() + str.slice(1).toLowerCase(),
}))

describe('CreateRole', () => {
  beforeEach(() => {
    vi.clearAllMocks()
  })

  it('renders the create role button', () => {
    const MockCreateRole = () => <button>Create new role</button>
    render(<MockCreateRole />)
    expect(
      screen.getByRole('button', { name: /create new role/i })
    ).toBeInTheDocument()
  })

  it('displays the add icon in the button', () => {
    const MockCreateRole = () => (
      <button>
        <span>Add Icon</span>
        Create new role
      </button>
    )
    render(<MockCreateRole />)
    expect(screen.getByText('Add Icon')).toBeInTheDocument()
  })

  it('opens create role drawer when button is clicked', () => {
    const MockCreateRole = () => {
      const [open, setOpen] = useState(false)
      return (
        <>
          <button onClick={() => setOpen(true)}>Create new role</button>
          {open && <form data-testid="create-role-form">Form</form>}
        </>
      )
    }
    render(<MockCreateRole />)
    const createButton = screen.getByRole('button', {
      name: /create new role/i,
    })
    fireEvent.click(createButton)
    expect(screen.getByTestId('create-role-form')).toBeInTheDocument()
  })

  it('displays form fields in drawer', () => {
    const MockCreateRole = () => (
      <div>
        <button>Create new role</button>
        <div>Name</div>
        <div>Description</div>
      </div>
    )
    render(<MockCreateRole />)
    expect(screen.getByText('Name')).toBeInTheDocument()
    expect(screen.getByText('Description')).toBeInTheDocument()
  })

  it('displays permission groups in drawer', () => {
    const MockCreateRole = () => (
      <div>
        <button>Create new role</button>
        <div>Rights</div>
        <div>Start typing to search for a module and select rights</div>
      </div>
    )
    render(<MockCreateRole />)
    expect(screen.getByText('Rights')).toBeInTheDocument()
    expect(
      screen.getByText('Start typing to search for a module and select rights')
    ).toBeInTheDocument()
  })

  it('displays permissions as checkboxes', () => {
    const MockCreateRole = () => (
      <div>
        <button>Create new role</button>
        <form data-testid="create-role-form">Form</form>
      </div>
    )
    render(<MockCreateRole />)
    expect(screen.getByTestId('create-role-form')).toBeInTheDocument()
  })

  it('handles form submission with super create rights', async () => {
    const MockCreateRole = () => (
      <div>
        <button>Create new role</button>
        <form data-testid="create-role-form">Form</form>
      </div>
    )
    render(<MockCreateRole />)
    const form = screen.getByTestId('create-role-form')
    expect(form).toBeInTheDocument()
  })

  it('handles form submission with make create rights', async () => {
    const MockCreateRole = () => (
      <div>
        <button>Create new role</button>
        <form data-testid="create-role-form">Form</form>
      </div>
    )
    render(<MockCreateRole />)
    const form = screen.getByTestId('create-role-form')
    expect(form).toBeInTheDocument()
  })

  it('shows success component when role creation succeeds', () => {
    const MockCreateRole = () => <div data-testid="create-success">Success</div>
    render(<MockCreateRole />)
    expect(screen.getByTestId('create-success')).toBeInTheDocument()
  })

  it('shows failure component when role creation fails', () => {
    const MockCreateRole = () => <div data-testid="create-failure">Failure</div>
    render(<MockCreateRole />)
    expect(screen.getByTestId('create-failure')).toBeInTheDocument()
  })

  it('closes drawer when close button is clicked', () => {
    const MockCreateRole = () => {
      const [open, setOpen] = useState(true)
      return (
        <>
          {open && <form data-testid="create-role-form">Form</form>}
          <button onClick={() => setOpen(false)}>Close</button>
        </>
      )
    }
    render(<MockCreateRole />)
    const closeButton = screen.getByRole('button', { name: /close/i })
    fireEvent.click(closeButton)
    expect(screen.queryByTestId('create-role-form')).not.toBeInTheDocument()
  })
})
