import { render, screen, fireEvent, waitFor } from '../../test-utils'
import { vi, describe, it, expect, beforeEach } from 'vitest'
import { ListRoles } from '../../../src/app/roles/ListRoles'
import { useAppDispatch, useAppSelector } from '@/store'
import * as rolesActions from '@/store/actions'

// Mock the store hooks
vi.mock('@/store', () => ({
  useAppDispatch: vi.fn(),
  useAppSelector: vi.fn(),
}))

// Mock the actions
vi.mock('@/store/actions', () => ({
  getRolesFilter: vi.fn(),
}))

// Mock the components
vi.mock('@dtbx/ui/components/Table', () => ({
  CustomPagination: ({ options, handlePagination }: any) => (
    <div data-testid="custom-pagination">
      <button
        onClick={() => handlePagination({ page: 2, size: 10, totalPages: 5 })}
      >
        Next Page
      </button>
      <span>
        Page {options.page} of {options.totalPages}
      </span>
    </div>
  ),
  CustomTableHeader: ({
    headLabel,
    onSelectAllClick,
    onRequestSort,
    showCheckbox,
  }: any) => (
    <thead data-testid="custom-table-header">
      <tr>
        {showCheckbox && (
          <th>
            <input
              type="checkbox"
              data-testid="select-all-checkbox"
              onChange={onSelectAllClick}
            />
          </th>
        )}
        {headLabel.map((header: any) => (
          <th key={header.id}>
            <button onClick={(e) => onRequestSort(e, header.id)}>
              {header.label}
            </button>
          </th>
        ))}
      </tr>
    </thead>
  ),
}))

vi.mock('@dtbx/ui/components/CheckBox', () => ({
  CustomCheckBox: ({ checked, slotProps }: any) => (
    <input
      type="checkbox"
      checked={checked}
      onChange={() => {}}
      data-testid="row-checkbox"
      {...slotProps?.input}
    />
  ),
}))

vi.mock('@dtbx/ui/components/Chip', () => ({
  CustomActiveChip: ({ label }: any) => (
    <div data-testid="active-chip">{label}</div>
  ),
  CustomErrorChip: ({ label }: any) => (
    <div data-testid="error-chip">{label}</div>
  ),
  CustomChip: ({ label }: any) => <div data-testid="custom-chip">{label}</div>,
}))

// Mock the RolesMoreMenu and ViewPermissions components
vi.mock('@/app/roles/RolesMoreMenu', () => ({
  RolesMoreMenu: ({ role }: any) => (
    <div data-testid="roles-more-menu">
      <button>Actions for {role.name}</button>
    </div>
  ),
  ViewPermissions: ({ role }: any) => (
    <div data-testid="view-permissions">
      <button>+{role.permissions.length - 2} more</button>
    </div>
  ),
}))

vi.mock('tiny-case', () => ({
  sentenceCase: (str: string) =>
    str.charAt(0).toUpperCase() + str.slice(1).toLowerCase(),
}))

describe('ListRoles', () => {
  const mockDispatch = vi.fn()
  const mockGetRolesFilter = vi.fn()
  const mockOnExport = vi.fn()
  const mockSetPage = vi.fn()

  const mockRoles = [
    {
      id: '1',
      name: 'admin',
      description: 'Administrator role',
      custom: true,
      permissions: [
        { id: '1', name: 'CREATE_USER' },
        { id: '2', name: 'UPDATE_USER' },
        { id: '3', name: 'DELETE_USER' },
      ],
    },
    {
      id: '2',
      name: 'manager',
      description: 'Manager role',
      custom: false,
      permissions: [
        { id: '1', name: 'CREATE_USER' },
        { id: '2', name: 'UPDATE_USER' },
      ],
    },
    {
      id: '3',
      name: 'user',
      description: 'Basic user role',
      custom: false,
      permissions: [{ id: '1', name: 'VIEW_USER' }],
    },
  ]

  const mockState = {
    tableListRoles: mockRoles,
    pageCount: 3,
  }

  beforeEach(() => {
    vi.clearAllMocks()
    vi.mocked(useAppDispatch).mockReturnValue(mockDispatch)
    vi.mocked(useAppSelector).mockReturnValue(mockState)
    vi.mocked(rolesActions.getRolesFilter).mockImplementation(
      mockGetRolesFilter
    )
  })

  it('renders the roles table with data', () => {
    render(<ListRoles onExport={mockOnExport} page={1} setPage={mockSetPage} />)

    expect(screen.getByTestId('custom-table-header')).toBeInTheDocument()
    expect(screen.getByText('Admin')).toBeInTheDocument()
    expect(screen.getByText('Manager')).toBeInTheDocument()
    expect(screen.getByText('User')).toBeInTheDocument()
  })

  it('displays correct table headers', () => {
    render(<ListRoles onExport={mockOnExport} page={1} setPage={mockSetPage} />)

    expect(screen.getByText('Role')).toBeInTheDocument()
    expect(screen.getByText('Is Custom')).toBeInTheDocument()
    expect(screen.getByText('Rights')).toBeInTheDocument()
    expect(screen.getByText('Description')).toBeInTheDocument()
    expect(screen.getByText('Actions')).toBeInTheDocument()
  })

  it('displays custom status chips correctly', () => {
    render(<ListRoles onExport={mockOnExport} page={1} setPage={mockSetPage} />)

    const activeChips = screen.getAllByTestId('active-chip')
    const errorChips = screen.getAllByTestId('error-chip')

    expect(activeChips).toHaveLength(1) // Only admin is custom
    expect(errorChips).toHaveLength(2) // Manager and user are not custom
    expect(activeChips[0]).toHaveTextContent('Yes')
    expect(errorChips[0]).toHaveTextContent('No')
  })

  it('displays role descriptions correctly', () => {
    render(<ListRoles onExport={mockOnExport} page={1} setPage={mockSetPage} />)

    expect(screen.getByText('Administrator role')).toBeInTheDocument()
    expect(screen.getByText('Manager role')).toBeInTheDocument()
    expect(screen.getByText('Basic user role')).toBeInTheDocument()
  })

  it('displays permission chips correctly', () => {
    render(<ListRoles onExport={mockOnExport} page={1} setPage={mockSetPage} />)

    const permissionChips = screen.getAllByTestId('custom-chip')
    expect(permissionChips.length).toBeGreaterThan(0)
    expect(permissionChips[0]).toHaveTextContent('create_user')
    expect(permissionChips[1]).toHaveTextContent('update_user')
  })

  it('shows ViewPermissions component when there are more than 2 permissions', () => {
    render(<ListRoles onExport={mockOnExport} page={1} setPage={mockSetPage} />)

    const viewPermissionsComponents = screen.getAllByTestId('view-permissions')
    expect(viewPermissionsComponents).toHaveLength(1) // Only admin has more than 2 permissions
    expect(viewPermissionsComponents[0]).toHaveTextContent('+1 more')
  })

  it('renders more menu for each role', () => {
    render(<ListRoles onExport={mockOnExport} page={1} setPage={mockSetPage} />)

    const moreMenus = screen.getAllByTestId('roles-more-menu')
    expect(moreMenus).toHaveLength(3)
    expect(screen.getByText('Actions for admin')).toBeInTheDocument()
    expect(screen.getByText('Actions for manager')).toBeInTheDocument()
    expect(screen.getByText('Actions for user')).toBeInTheDocument()
  })

  it('handles row selection', () => {
    render(<ListRoles onExport={mockOnExport} page={1} setPage={mockSetPage} />)

    const firstRowCheckbox = screen.getAllByTestId('row-checkbox')[0]
    fireEvent.click(firstRowCheckbox.parentElement!)

    expect(firstRowCheckbox).toBeChecked()
  })

  it('handles select all functionality', () => {
    render(<ListRoles onExport={mockOnExport} page={1} setPage={mockSetPage} />)

    const selectAllCheckbox = screen.getByTestId('select-all-checkbox')
    fireEvent.change(selectAllCheckbox, { target: { checked: true } })

    // The select all functionality should trigger the handler
    expect(selectAllCheckbox).toBeInTheDocument()
  })

  it('handles table sorting', () => {
    render(<ListRoles onExport={mockOnExport} page={1} setPage={mockSetPage} />)

    const roleHeaderButton = screen.getByText('Role')
    fireEvent.click(roleHeaderButton)

    // Should trigger sort functionality
    expect(roleHeaderButton).toBeInTheDocument()
  })

  it('handles pagination', async () => {
    render(<ListRoles onExport={mockOnExport} page={1} setPage={mockSetPage} />)

    const nextPageButton = screen.getByText('Next Page')
    fireEvent.click(nextPageButton)

    await waitFor(() => {
      expect(mockGetRolesFilter).toHaveBeenCalledWith(mockDispatch, {
        page: 2,
        size: 10,
        totalPages: 5,
      })
      expect(mockSetPage).toHaveBeenCalledWith(2)
    })
  })

  it('renders pagination when pageCount > 0', () => {
    render(<ListRoles onExport={mockOnExport} page={1} setPage={mockSetPage} />)

    expect(screen.getByTestId('custom-pagination')).toBeInTheDocument()
    expect(screen.getByText('Page 1 of 3')).toBeInTheDocument()
  })

  it('does not render pagination when pageCount is 0', () => {
    vi.mocked(useAppSelector).mockReturnValue({
      ...mockState,
      pageCount: 0,
    })

    render(<ListRoles onExport={mockOnExport} page={1} setPage={mockSetPage} />)

    expect(screen.queryByTestId('custom-pagination')).not.toBeInTheDocument()
  })

  it('handles empty roles array', () => {
    vi.mocked(useAppSelector).mockReturnValue({
      ...mockState,
      tableListRoles: [],
    })

    render(<ListRoles onExport={mockOnExport} page={1} setPage={mockSetPage} />)

    expect(screen.getByTestId('custom-table-header')).toBeInTheDocument()
    expect(screen.queryByText('Admin')).not.toBeInTheDocument()
  })

  it('handles roles without permissions', () => {
    const rolesWithoutPermissions = [
      {
        ...mockRoles[0],
        permissions: [],
      },
    ]

    vi.mocked(useAppSelector).mockReturnValue({
      ...mockState,
      tableListRoles: rolesWithoutPermissions,
    })

    render(<ListRoles onExport={mockOnExport} page={1} setPage={mockSetPage} />)

    expect(screen.getByTestId('custom-table-header')).toBeInTheDocument()
    expect(screen.queryByTestId('view-permissions')).not.toBeInTheDocument()
  })

  it('handles roles with exactly 2 permissions', () => {
    const rolesWithTwoPermissions = [
      {
        ...mockRoles[0],
        permissions: [
          { id: '1', name: 'CREATE_USER' },
          { id: '2', name: 'UPDATE_USER' },
        ],
      },
    ]

    vi.mocked(useAppSelector).mockReturnValue({
      ...mockState,
      tableListRoles: rolesWithTwoPermissions,
    })

    render(<ListRoles onExport={mockOnExport} page={1} setPage={mockSetPage} />)

    const permissionChips = screen.getAllByTestId('custom-chip')
    expect(permissionChips).toHaveLength(2)
    expect(screen.queryByTestId('view-permissions')).not.toBeInTheDocument()
  })

  it('applies correct styling to the table container', () => {
    render(<ListRoles onExport={mockOnExport} page={1} setPage={mockSetPage} />)

    const table = screen.getByRole('table')
    expect(table).toHaveAttribute('aria-label', 'designations table')
  })

  it('handles sorting by different columns', () => {
    render(<ListRoles onExport={mockOnExport} page={1} setPage={mockSetPage} />)

    // Test sorting by different columns
    const customHeader = screen.getByText('Is Custom')
    fireEvent.click(customHeader)

    const rightsHeader = screen.getByText('Rights')
    fireEvent.click(rightsHeader)

    const descriptionHeader = screen.getByText('Description')
    fireEvent.click(descriptionHeader)

    // All headers should be clickable
    expect(customHeader).toBeInTheDocument()
    expect(rightsHeader).toBeInTheDocument()
    expect(descriptionHeader).toBeInTheDocument()
  })

  it('handles multiple row selections', () => {
    render(<ListRoles onExport={mockOnExport} page={1} setPage={mockSetPage} />)

    // Select first row
    const firstRow = screen.getAllByRole('checkbox')[0].closest('tr')
    if (firstRow) {
      fireEvent.click(firstRow)
    }

    // Select second row
    const secondRow = screen.getAllByRole('checkbox')[1].closest('tr')
    if (secondRow) {
      fireEvent.click(secondRow)
    }

    const rowCheckboxes = screen.getAllByTestId('row-checkbox')
    expect(rowCheckboxes).toHaveLength(3)
  })

  it('handles deselecting individual rows', () => {
    render(<ListRoles onExport={mockOnExport} page={1} setPage={mockSetPage} />)

    // Select first row
    const firstRow = screen.getAllByRole('checkbox')[0].closest('tr')
    if (firstRow) {
      fireEvent.click(firstRow)
      // Deselect first row
      fireEvent.click(firstRow)
    }

    const firstRowCheckbox = screen.getAllByTestId('row-checkbox')[0]
    expect(firstRowCheckbox).toBeInTheDocument()
  })

  it('calls onExport with selected IDs', () => {
    render(<ListRoles onExport={mockOnExport} page={1} setPage={mockSetPage} />)

    // The onExport function should be available for use
    expect(mockOnExport).toBeDefined()
  })

  it('handles roles with long names and descriptions', () => {
    const rolesWithLongText = [
      {
        ...mockRoles[0],
        name: 'very_long_role_name_that_might_cause_layout_issues',
        description:
          'This is a very long description that might cause layout issues in the table',
      },
    ]

    vi.mocked(useAppSelector).mockReturnValue({
      ...mockState,
      tableListRoles: rolesWithLongText,
    })

    render(<ListRoles onExport={mockOnExport} page={1} setPage={mockSetPage} />)

    expect(
      screen.getByText('Very_long_role_name_that_might_cause_layout_issues')
    ).toBeInTheDocument()
    expect(
      screen.getByText(
        'This is a very long description that might cause layout issues in the table'
      )
    ).toBeInTheDocument()
  })
})
