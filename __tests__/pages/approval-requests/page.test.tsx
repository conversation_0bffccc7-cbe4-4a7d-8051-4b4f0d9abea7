import { render, screen } from '../../test-utils'
import { vi, describe, it, expect, beforeEach } from 'vitest'
import ApprovalRequestsPage from '../../../src/app/approval-requests/page'
import { useAppDispatch, useAppSelector } from '@/store'

// Mock the dependencies
vi.mock('@/store', () => ({
  useAppDispatch: vi.fn(),
  useAppSelector: vi.fn(),
}))

vi.mock('@/store/actions', () => ({
  getApprovals: vi.fn(),
  setSelectedApprovalRequest: vi.fn(),
}))

// Mock the tabs components
vi.mock('@dtbx/ui/components/Tabs', () => ({
  AntTab: ({ label }: { label: React.ReactNode }) => (
    <div data-testid="ant-tab">{label}</div>
  ),
  AntTabs: ({
    children,
    value: _value,
    onChange: _onChange,
  }: {
    children: React.ReactNode
    value: number
    onChange: (event: React.SyntheticEvent, newValue: number) => void
  }) => <div data-testid="ant-tabs">{children}</div>,
  TabPanel: ({
    children,
    value,
    index,
  }: {
    children: React.ReactNode
    value: number
    index: number
  }) => (
    <div
      data-testid="tab-panel"
      style={{ display: value === index ? 'block' : 'none' }}
    >
      {children}
    </div>
  ),
}))

// Mock the Pending and All components
vi.mock('../../../src/app/approval-requests/Pending', () => ({
  default: () => <div data-testid="pending-component">Pending Component</div>,
}))

vi.mock('../../../src/app/approval-requests/All', () => ({
  default: () => <div data-testid="all-component">All Component</div>,
}))

// Mock the AllApprovalRequests component
vi.mock('../../../src/app/approval-requests/All/AllApprovalRequests', () => ({
  AllApprovalRequests: () => (
    <div data-testid="all-approval-requests">All Approval Requests</div>
  ),
}))

// Mock the PendingApprovalRequests component
vi.mock(
  '../../../src/app/approval-requests/Pending/PendingApprovalRequests',
  () => ({
    PendingApprovalRequests: () => (
      <div data-testid="pending-approval-requests">
        Pending Approval Requests
      </div>
    ),
  })
)

// Mock the ApprovedApprovalRequests component
vi.mock(
  '../../../src/app/approval-requests/Approved/ApprovedApprovalRequests',
  () => ({
    ApprovedApprovalRequests: () => (
      <div data-testid="approved-approval-requests">
        Approved Approval Requests
      </div>
    ),
  })
)

// Mock the RejectedApprovalRequests component
vi.mock(
  '../../../src/app/approval-requests/Rejected/RejectedApprovalRequests',
  () => ({
    RejectedApprovalRequests: () => (
      <div data-testid="rejected-approval-requests">
        Rejected Approval Requests
      </div>
    ),
  })
)

describe('ApprovalRequestsPage', () => {
  const mockDispatch = vi.fn()
  const mockApprovalRequests = [
    {
      id: '1',
      requestType: 'CREATE_USER',
      status: 'PENDING',
      createdBy: 'user1',
      createdAt: '2023-01-01',
    },
    {
      id: '2',
      requestType: 'UPDATE_ROLE',
      status: 'APPROVED',
      createdBy: 'user2',
      createdAt: '2023-01-02',
    },
  ]

  beforeEach(() => {
    vi.clearAllMocks()
    vi.mocked(useAppDispatch).mockReturnValue(mockDispatch)
    vi.mocked(useAppSelector).mockImplementation((_selector) => {
      // Mock different state slices based on the selector
      return {
        approvalRequestResponse: {
          data: mockApprovalRequests,
          totalElements: 20,
          totalNumberOfPages: 2,
          pageNumber: 1,
          pageSize: 10,
        },
        approvalRequests: mockApprovalRequests,
        isLoadingRequests: false,
        requestTypes: [],
        isRequestTypesLoading: false,
        isRequestTypesSuccess: false,
        search: {
          searchBy: ['firstName'],
          searchValue: '',
        },
      }
    })
  })

  it('renders the Approval Requests page with correct title', () => {
    render(<ApprovalRequestsPage />)

    expect(screen.getByText('Approval Requests')).toBeInTheDocument()
  })

  it('renders the tabs component with correct tabs', () => {
    render(<ApprovalRequestsPage />)

    const tabsElement = screen.getByTestId('ant-tabs')
    expect(tabsElement).toBeInTheDocument()

    expect(screen.getByText('Pending Requests')).toBeInTheDocument()
    expect(screen.getByText('All Requests')).toBeInTheDocument()
  })

  it('shows the Pending Requests tab by default', () => {
    render(<ApprovalRequestsPage />)

    expect(screen.getByTestId('pending-component')).toBeInTheDocument()
  })

  it('changes tab when a different tab is clicked', () => {
    render(<ApprovalRequestsPage />)

    expect(screen.getByTestId('pending-component')).toBeInTheDocument()

    expect(screen.queryByTestId('all-component')).not.toBeVisible()
  })

  it('renders the icon and title correctly', () => {
    render(<ApprovalRequestsPage />)

    expect(screen.getByText('Approval Requests')).toBeInTheDocument()
  })

  it('renders with different approval request data', () => {
    const customMockApprovalRequests = [
      {
        id: '3',
        requestType: 'ROLE_ASSIGNMENT',
        status: 'PENDING',
        createdAt: '2023-01-03',
        user: {
          id: '3',
          firstName: 'Bob',
          lastName: 'Johnson',
          email: '<EMAIL>',
        },
      },
    ]

    vi.mocked(useAppSelector).mockImplementation((_selector) => {
      return {
        approvalRequestResponse: {
          data: customMockApprovalRequests,
          totalElements: 1,
          totalNumberOfPages: 1,
          pageNumber: 1,
          pageSize: 10,
        },
        approvalRequests: customMockApprovalRequests,
        isLoadingRequests: false,
        requestTypes: [],
        isRequestTypesLoading: false,
        isRequestTypesSuccess: false,
        search: {
          searchBy: ['firstName'],
          searchValue: '',
        },
      }
    })

    render(<ApprovalRequestsPage />)

    expect(screen.getByTestId('pending-component')).toBeInTheDocument()
  })

  it('handles empty approval requests state', () => {
    vi.mocked(useAppSelector).mockImplementation((_selector) => {
      return {
        approvalRequestResponse: {
          data: [],
          totalElements: 0,
          totalNumberOfPages: 0,
          pageNumber: 1,
          pageSize: 10,
        },
        approvalRequests: [],
        isLoadingRequests: false,
        requestTypes: [],
        isRequestTypesLoading: false,
        isRequestTypesSuccess: false,
        search: {
          searchBy: ['firstName'],
          searchValue: '',
        },
      }
    })

    render(<ApprovalRequestsPage />)

    expect(screen.getByTestId('pending-component')).toBeInTheDocument()
  })

  it('renders with loading state', () => {
    vi.mocked(useAppSelector).mockImplementation((_selector) => {
      return {
        approvalRequestResponse: {
          data: [],
          totalElements: 0,
          totalNumberOfPages: 0,
          pageNumber: 1,
          pageSize: 10,
        },
        approvalRequests: [],
        isLoadingRequests: true,
        requestTypes: [],
        isRequestTypesLoading: true,
        isRequestTypesSuccess: false,
        search: {
          searchBy: ['firstName'],
          searchValue: '',
        },
      }
    })

    render(<ApprovalRequestsPage />)

    expect(screen.getByTestId('pending-component')).toBeInTheDocument()
  })
})
