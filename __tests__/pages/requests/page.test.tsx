import { render, screen, fireEvent, waitFor } from '../../test-utils'
import { vi, describe, it, expect, beforeEach } from 'vitest'
import Requests from '../../../src/app/requests/page'
import { useAppDispatch, useAppSelector } from '@/store'
import * as approvalRequestsActions from '@/store/actions/approvalRequests'

// Mock the store hooks
vi.mock('@/store', () => ({
  useAppDispatch: vi.fn(),
  useAppSelector: vi.fn(),
}))

// Mock the actions
vi.mock('@/store/actions/approvalRequests', () => ({
  getApprovalRequestTypes: vi.fn(),
  getApprovals: vi.fn(),
}))

// Mock the components
vi.mock('@/app/users/approval-requests/RequestsMoreMenu', () => ({
  RequestsMoreMenu: ({ request }: any) => (
    <div data-testid="requests-more-menu">
      <button>Actions</button>
      <span>{request.id}</span>
    </div>
  ),
}))

vi.mock('@/app/users/approval-requests/pageHeader', () => ({
  default: () => <div data-testid="page-header">Page Header</div>,
}))

vi.mock('../../../src/app/approval-requests/Pending', () => ({
  RequestChip: ({ label }: any) => (
    <div data-testid="request-chip">{label}</div>
  ),
}))

vi.mock('@/app/roles/ListRoles', () => ({
  CustomTableCell: ({ children, ...props }: any) => (
    <td data-testid="custom-table-cell" {...props}>
      {children}
    </td>
  ),
}))

// Mock UI components
vi.mock('@dtbx/ui/components/Table', () => ({
  CustomPagination: ({ options, handlePagination }: any) => (
    <div data-testid="custom-pagination">
      <button
        onClick={() => handlePagination({ page: 2, size: 10, totalPages: 5 })}
      >
        Next Page
      </button>
      <span>
        Page {options.page} of {options.totalPages}
      </span>
    </div>
  ),
  CustomTableHeader: ({ headLabel }: any) => (
    <thead data-testid="custom-table-header">
      <tr>
        {headLabel.map((header: any) => (
          <th key={header.id}>{header.label}</th>
        ))}
      </tr>
    </thead>
  ),
}))

vi.mock('@dtbx/ui/components/Chip', () => ({
  CustomerStatusChip: ({ label }: any) => (
    <div data-testid="customer-status-chip">{label}</div>
  ),
}))

vi.mock('@dtbx/ui/components/Loading', () => ({
  CustomSkeleton: (props: any) => (
    <div data-testid="custom-skeleton" {...props}>
      Loading...
    </div>
  ),
}))

vi.mock('@dtbx/ui/components/EmptyPage', () => ({
  EmptySearchAndFilter: ({ message, additionalText, onClick }: any) => (
    <div data-testid="empty-search-filter">
      <p>{message}</p>
      <p>{additionalText}</p>
      <button onClick={onClick}>Retry</button>
    </div>
  ),
}))

vi.mock('@dtbx/store/utils', () => ({
  formatTimestamp: (timestamp: string) => `Formatted: ${timestamp}`,
}))

describe('Requests Page', () => {
  const mockDispatch = vi.fn()
  const mockGetApprovals = vi.fn()
  const mockGetApprovalRequestTypes = vi.fn()

  const mockApprovalRequests = [
    {
      id: '1',
      maker: 'John Doe',
      dateCreated: '2024-01-15T10:30:00Z',
      status: 'PENDING',
      makerCheckerType: {
        name: 'CREATE_USER',
        module: 'users',
        channel: 'DBP',
        checkerPermissions: [],
        makerPermissions: [],
        overridePermissions: [],
        type: 'CREATE',
      },
      diff: [],
    },
    {
      id: '2',
      maker: 'Alice Johnson',
      dateCreated: '2024-01-14T09:15:00Z',
      status: 'APPROVED',
      makerCheckerType: {
        name: 'UPDATE_USER',
        module: 'users',
        channel: 'DBP',
        checkerPermissions: [],
        makerPermissions: [],
        overridePermissions: [],
        type: 'UPDATE',
      },
      diff: [],
    },
  ]

  const mockState = {
    approvalRequests: {
      isLoadingRequests: false,
      approvalRequestResponse: {
        data: mockApprovalRequests,
        pageNumber: 1,
        pageSize: 10,
        totalNumberOfPages: 3,
        totalElements: 25,
      },
      userApprovalRequestFilters: { module: 'users' },
    },
  }

  beforeEach(() => {
    vi.clearAllMocks()
    vi.mocked(useAppDispatch).mockReturnValue(mockDispatch)
    vi.mocked(useAppSelector).mockReturnValue(mockState.approvalRequests)
    vi.mocked(approvalRequestsActions.getApprovals).mockImplementation(
      mockGetApprovals
    )
    vi.mocked(
      approvalRequestsActions.getApprovalRequestTypes
    ).mockImplementation(mockGetApprovalRequestTypes)
  })

  it('renders the requests page with approval requests', () => {
    render(<Requests />)

    expect(screen.getByTestId('page-header')).toBeInTheDocument()
    expect(screen.getByTestId('custom-table-header')).toBeInTheDocument()
    expect(screen.getByText('John Doe')).toBeInTheDocument()
    expect(screen.getByText('Alice Johnson')).toBeInTheDocument()
  })

  it('shows loading skeleton when data is loading', () => {
    vi.mocked(useAppSelector).mockReturnValue({
      ...mockState.approvalRequests,
      isLoadingRequests: true,
    })

    render(<Requests />)

    expect(screen.getByTestId('custom-skeleton')).toBeInTheDocument()
    expect(screen.queryByTestId('custom-table-header')).not.toBeInTheDocument()
  })

  it('shows empty state when no requests are available', () => {
    vi.mocked(useAppSelector).mockReturnValue({
      ...mockState.approvalRequests,
      approvalRequestResponse: {
        ...mockState.approvalRequests.approvalRequestResponse,
        data: [],
      },
    })

    render(<Requests />)

    expect(screen.getByTestId('empty-search-filter')).toBeInTheDocument()
    expect(
      screen.getByText('No requests match your filters')
    ).toBeInTheDocument()
  })

  it('calls getApprovals when retry button is clicked in empty state', async () => {
    vi.mocked(useAppSelector).mockReturnValue({
      ...mockState.approvalRequests,
      approvalRequestResponse: {
        ...mockState.approvalRequests.approvalRequestResponse,
        data: [],
      },
    })

    render(<Requests />)

    const retryButton = screen.getByText('Retry')
    fireEvent.click(retryButton)

    expect(mockGetApprovals).toHaveBeenCalledWith(
      mockDispatch,
      '?channel=DBP&module=users&page=1&size=10'
    )
  })

  it('displays request information correctly', () => {
    render(<Requests />)

    // Check request chips
    expect(screen.getByText('CREATE_USER')).toBeInTheDocument()
    expect(screen.getByText('UPDATE_USER')).toBeInTheDocument()

    // Check modules
    expect(screen.getAllByText('users')).toHaveLength(2)

    // Check formatted timestamps
    expect(
      screen.getByText('Formatted: 2024-01-15T10:30:00Z')
    ).toBeInTheDocument()
    expect(
      screen.getByText('Formatted: 2024-01-14T09:15:00Z')
    ).toBeInTheDocument()

    // Check status chips
    expect(screen.getByText('PENDING')).toBeInTheDocument()
    expect(screen.getByText('APPROVED')).toBeInTheDocument()

    // Check makers
    expect(screen.getByText('John Doe')).toBeInTheDocument()
    expect(screen.getByText('Alice Johnson')).toBeInTheDocument()
  })

  it('renders more menu for each request', () => {
    render(<Requests />)

    const moreMenus = screen.getAllByTestId('requests-more-menu')
    expect(moreMenus).toHaveLength(2)
    expect(screen.getAllByText('Actions')).toHaveLength(2)
  })

  it('renders pagination when there are multiple pages', () => {
    render(<Requests />)

    expect(screen.getByTestId('custom-pagination')).toBeInTheDocument()
    expect(screen.getByText('Page 1 of 3')).toBeInTheDocument()
  })

  it('does not render pagination when totalNumberOfPages is 0', () => {
    vi.mocked(useAppSelector).mockReturnValue({
      ...mockState.approvalRequests,
      approvalRequestResponse: {
        ...mockState.approvalRequests.approvalRequestResponse,
        totalNumberOfPages: 0,
      },
    })

    render(<Requests />)

    expect(screen.queryByTestId('custom-pagination')).not.toBeInTheDocument()
  })

  it('handles pagination changes', async () => {
    render(<Requests />)

    const nextPageButton = screen.getByText('Next Page')
    fireEvent.click(nextPageButton)

    await waitFor(() => {
      expect(mockGetApprovals).toHaveBeenCalledWith(
        mockDispatch,
        '?channel=DBP&module=users&page=2&size=10'
      )
    })
  })

  it('calls getApprovalRequestTypes and getApprovals on mount', () => {
    render(<Requests />)

    expect(mockGetApprovalRequestTypes).toHaveBeenCalledWith(
      mockDispatch,
      'DBP'
    )
    expect(mockGetApprovals).toHaveBeenCalledWith(
      mockDispatch,
      '?channel=DBP&module=users&page=1&size=10'
    )
  })

  it('builds query params correctly', () => {
    render(<Requests />)

    // The buildQueryParams function is tested indirectly through pagination
    const nextPageButton = screen.getByText('Next Page')
    fireEvent.click(nextPageButton)

    expect(mockGetApprovals).toHaveBeenCalledWith(
      mockDispatch,
      '?channel=DBP&module=users&page=2&size=10'
    )
  })

  it('renders table headers correctly', () => {
    render(<Requests />)

    expect(screen.getByText('Request type')).toBeInTheDocument()
    expect(screen.getByText('Module')).toBeInTheDocument()
    expect(screen.getByText('Maker Timestamp')).toBeInTheDocument()
    expect(screen.getByText('Status')).toBeInTheDocument()
    expect(screen.getByText('Maker')).toBeInTheDocument()
    expect(screen.getByText('Action')).toBeInTheDocument()
  })

  it('handles requests with different statuses', () => {
    const requestsWithDifferentStatuses = [
      { ...mockApprovalRequests[0], status: 'PENDING' },
      { ...mockApprovalRequests[1], status: 'APPROVED' },
      { ...mockApprovalRequests[0], id: '3', status: 'REJECTED' },
    ]

    vi.mocked(useAppSelector).mockReturnValue({
      ...mockState.approvalRequests,
      approvalRequestResponse: {
        ...mockState.approvalRequests.approvalRequestResponse,
        data: requestsWithDifferentStatuses,
      },
    })

    render(<Requests />)

    expect(screen.getByText('PENDING')).toBeInTheDocument()
    expect(screen.getByText('APPROVED')).toBeInTheDocument()
    expect(screen.getByText('REJECTED')).toBeInTheDocument()
  })

  it('handles requests with different modules', () => {
    const requestsWithDifferentModules = [
      {
        ...mockApprovalRequests[0],
        makerCheckerType: {
          ...mockApprovalRequests[0].makerCheckerType,
          module: 'users',
        },
      },
      {
        ...mockApprovalRequests[1],
        makerCheckerType: {
          ...mockApprovalRequests[1].makerCheckerType,
          module: 'roles',
        },
      },
    ]

    vi.mocked(useAppSelector).mockReturnValue({
      ...mockState.approvalRequests,
      approvalRequestResponse: {
        ...mockState.approvalRequests.approvalRequestResponse,
        data: requestsWithDifferentModules,
      },
    })

    render(<Requests />)

    expect(screen.getByText('users')).toBeInTheDocument()
    expect(screen.getByText('roles')).toBeInTheDocument()
  })

  it('handles empty approvalRequestResponse gracefully', () => {
    vi.mocked(useAppSelector).mockReturnValue({
      ...mockState.approvalRequests,
      approvalRequestResponse: {
        data: undefined,
        pageNumber: 1,
        pageSize: 10,
        totalNumberOfPages: 0,
        totalElements: 0,
      },
    })

    render(<Requests />)

    expect(screen.getByTestId('empty-search-filter')).toBeInTheDocument()
  })

  it('handles pagination with different page sizes', async () => {
    render(<Requests />)

    // Mock a pagination change with different size
    const nextPageButton = screen.getByText('Next Page')

    // Simulate clicking pagination with size 20
    fireEvent.click(nextPageButton)

    await waitFor(() => {
      expect(mockGetApprovals).toHaveBeenCalledWith(
        mockDispatch,
        '?channel=DBP&module=users&page=2&size=10'
      )
    })
  })

  it('handles requests with missing makerCheckerType properties', () => {
    const requestsWithMissingProps = [
      {
        ...mockApprovalRequests[0],
        makerCheckerType: {
          ...mockApprovalRequests[0].makerCheckerType,
          name: undefined,
          module: undefined,
        },
      },
    ]

    vi.mocked(useAppSelector).mockReturnValue({
      ...mockState.approvalRequests,
      approvalRequestResponse: {
        ...mockState.approvalRequests.approvalRequestResponse,
        data: requestsWithMissingProps,
      },
    })

    render(<Requests />)

    // Component should still render without crashing
    expect(screen.getByTestId('custom-table-header')).toBeInTheDocument()
  })

  it('handles requests with long maker names', () => {
    const requestsWithLongNames = [
      {
        ...mockApprovalRequests[0],
        maker: 'Very Long Maker Name That Might Cause Layout Issues',
      },
    ]

    vi.mocked(useAppSelector).mockReturnValue({
      ...mockState.approvalRequests,
      approvalRequestResponse: {
        ...mockState.approvalRequests.approvalRequestResponse,
        data: requestsWithLongNames,
      },
    })

    render(<Requests />)

    expect(
      screen.getByText('Very Long Maker Name That Might Cause Layout Issues')
    ).toBeInTheDocument()
  })

  it('handles requests with special characters in data', () => {
    const requestsWithSpecialChars = [
      {
        ...mockApprovalRequests[0],
        maker: "John O'Connor & Associates",
        makerCheckerType: {
          ...mockApprovalRequests[0].makerCheckerType,
          name: 'CREATE_USER_WITH_SPECIAL_CHARS_@#$',
        },
      },
    ]

    vi.mocked(useAppSelector).mockReturnValue({
      ...mockState.approvalRequests,
      approvalRequestResponse: {
        ...mockState.approvalRequests.approvalRequestResponse,
        data: requestsWithSpecialChars,
      },
    })

    render(<Requests />)

    expect(screen.getByText("John O'Connor & Associates")).toBeInTheDocument()
    expect(
      screen.getByText('CREATE_USER_WITH_SPECIAL_CHARS_@#$')
    ).toBeInTheDocument()
  })

  it('handles pagination state updates correctly', async () => {
    render(<Requests />)

    // Initial state should show page 1
    expect(screen.getByText('Page 1 of 3')).toBeInTheDocument()

    // Click next page
    const nextPageButton = screen.getByText('Next Page')
    fireEvent.click(nextPageButton)

    // Should call getApprovals with new page
    await waitFor(() => {
      expect(mockGetApprovals).toHaveBeenCalledWith(
        mockDispatch,
        '?channel=DBP&module=users&page=2&size=10'
      )
    })
  })

  it('handles userApprovalRequestFilters with multiple filters', async () => {
    vi.mocked(useAppSelector).mockReturnValue({
      ...mockState.approvalRequests,
      userApprovalRequestFilters: {
        module: 'users',
        status: 'PENDING',
        requestType: 'CREATE_USER',
      },
    })

    render(<Requests />)

    const nextPageButton = screen.getByText('Next Page')
    fireEvent.click(nextPageButton)

    await waitFor(() => {
      expect(mockGetApprovals).toHaveBeenCalledWith(
        mockDispatch,
        '?channel=DBP&module=users&status=PENDING&requestType=CREATE_USER&page=2&size=10'
      )
    })
  })

  it('handles empty userApprovalRequestFilters', async () => {
    vi.mocked(useAppSelector).mockReturnValue({
      ...mockState.approvalRequests,
      userApprovalRequestFilters: {},
    })

    render(<Requests />)

    const nextPageButton = screen.getByText('Next Page')
    fireEvent.click(nextPageButton)

    await waitFor(() => {
      expect(mockGetApprovals).toHaveBeenCalledWith(
        mockDispatch,
        '?channel=DBP&module=users&page=2&size=10'
      )
    })
  })

  it('renders correct table structure', () => {
    render(<Requests />)

    // Check that table structure is correct
    const table = screen.getByRole('table')
    expect(table).toBeInTheDocument()

    // Check table headers
    const headers = [
      'Request type',
      'Module',
      'Maker Timestamp',
      'Status',
      'Maker',
      'Action',
    ]
    headers.forEach((header) => {
      expect(screen.getByText(header)).toBeInTheDocument()
    })

    // Check table rows
    const tableRows = screen.getAllByTestId('custom-table-cell')
    expect(tableRows.length).toBeGreaterThan(0)
  })

  it('handles requests with null or undefined dates', () => {
    const requestsWithNullDates = [
      {
        ...mockApprovalRequests[0],
        dateCreated: null,
      },
      {
        ...mockApprovalRequests[1],
        dateCreated: undefined,
      },
    ]

    vi.mocked(useAppSelector).mockReturnValue({
      ...mockState.approvalRequests,
      approvalRequestResponse: {
        ...mockState.approvalRequests.approvalRequestResponse,
        data: requestsWithNullDates,
      },
    })

    render(<Requests />)

    // Component should still render without crashing
    expect(screen.getByTestId('custom-table-header')).toBeInTheDocument()
  })
})
