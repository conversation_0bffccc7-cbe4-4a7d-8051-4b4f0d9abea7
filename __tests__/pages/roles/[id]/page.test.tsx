import { render, screen, fireEvent } from '@testing-library/react'
import { vi, describe, it, expect, beforeEach } from 'vitest'
import { Provider } from 'react-redux'
import { configureStore, type EnhancedStore } from '@reduxjs/toolkit'
import { RoleDetails } from '@/app/roles/details/RolesDetails/RoleDetails'

const rolesReducer = (
  state = {
    role: {
      id: '123',
      name: 'Admin',
      description: 'Administrator role',
      creationDate: '2023-01-01',
      custom: true,
      permissions: ['read', 'write'],
      permissionsGroup: [],
      status: 'APPROVED',
    },
    isLoadingUpdateRole: false,
    isLoadingDeleteRole: false,
  }
) => state

const authReducer = (
  state = {
    decodedToken: {
      permissions: [],
    },
  }
) => state

const navigationReducer = (
  state = {
    isSidebarCollapsed: false,
    switchToRoleDetails: {
      open: true,
      type: 'view',
    },
  }
) => state

const approvalRequestsReducer = (
  state = {
    selectedApprovalRequest: {
      status: 'PENDING',
      diff: {},
      makerCheckerType: {
        module: 'groups',
        type: 'CREATE',
      },
    },
  }
) => state

const notificationsReducer = (
  state = {
    localNotification: null,
    localNotificationType: null,
  }
) => state

// Mock the components
vi.mock('@dtbx/ui/components/Tabs', () => ({
  PageHeader: ({
    title,
    children,
  }: {
    title: string
    children: React.ReactNode
  }) => (
    <div data-testid="page-header">
      <h1>{title}</h1>
      <div>{children}</div>
    </div>
  ),
  Button: ({
    children,
    onClick,
    ...props
  }: {
    children: React.ReactNode
    onClick: () => void
  } & React.ButtonHTMLAttributes<HTMLButtonElement>) => (
    <button onClick={onClick} {...props}>
      {children}
    </button>
  ),
  LoadingDetailsSkeleton: () => (
    <div data-testid="loading-skeleton">Loading...</div>
  ),
  AccessControlWrapper: ({ children }: { children: React.ReactNode }) => (
    <div>{children}</div>
  ),
  AntTab: ({ label }: { label: string }) => (
    <div data-testid="ant-tab">{label}</div>
  ),
  AntTabs: ({ children }: { children: React.ReactNode }) => (
    <div data-testid="ant-tabs">{children}</div>
  ),
  TabPanel: ({
    children,
    value,
    index,
  }: {
    children: React.ReactNode
    value: number
    index: number
  }) => (
    <div
      data-testid={`tab-panel-${index}`}
      style={{ display: value === index ? 'block' : 'none' }}
    >
      {children}
    </div>
  ),
  StatusChip: ({ status }: { status: string }) => (
    <div data-testid="status-chip">{status}</div>
  ),
}))

// Mock the custom components
vi.mock('@/app/roles/details/RolesDetails/Rights', () => ({
  RoleRightsView: () => <div data-testid="role-rights-view">Rights View</div>,
}))

vi.mock('@/app/roles/details/RolesDetails/Users', () => ({
  RoleUsersView: () => <div data-testid="role-users-view">Users View</div>,
}))

vi.mock('@dtbx/ui/components/Chip', () => ({
  CustomErrorChip: ({ label }: { label: string }) => (
    <div data-testid="status-chip">{label}</div>
  ),
  CustomSuccessChip: ({ label }: { label: string }) => (
    <div data-testid="status-chip">{label}</div>
  ),
  CustomWarningChip: ({ label }: { label: string }) => (
    <div data-testid="status-chip">{label}</div>
  ),
}))

// Mock the router
vi.mock('next/navigation', async () => {
  const actual = await vi.importActual('next/navigation')
  return {
    ...actual,
    useParams: () => ({ id: '123' }),
    useRouter: () => ({
      push: vi.fn(),
      back: vi.fn(),
    }),
  }
})

// Mock the custom router
const mockPush = vi.fn()
vi.mock('@dtbx/ui/hooks', () => ({
  useCustomRouter: () => ({
    push: mockPush,
    back: vi.fn(),
  }),
}))

describe('RoleDetails', () => {
  let store: EnhancedStore

  beforeEach(() => {
    // Reset mocks
    vi.clearAllMocks()

    // Create a fresh store for each test
    store = configureStore({
      reducer: {
        roles: rolesReducer,
        auth: authReducer,
        navigation: navigationReducer,
        approvalRequests: approvalRequestsReducer,
        notifications: notificationsReducer,
      },
    })
  })

  it('renders without crashing', () => {
    render(
      <Provider store={store}>
        <RoleDetails />
      </Provider>
    )

    // Basic check that the component renders
    expect(screen.getByTestId('ant-tabs')).toBeInTheDocument()
  })

  it('displays the role name and status', () => {
    render(
      <Provider store={store}>
        <RoleDetails />
      </Provider>
    )

    const roleNameElements = screen.getAllByText('Admin')
    expect(roleNameElements.length).toBeGreaterThan(0)

    expect(screen.getByTestId('status-chip')).toBeInTheDocument()
  })

  it('displays tabs for Rights and Users', () => {
    render(
      <Provider store={store}>
        <RoleDetails />
      </Provider>
    )

    expect(screen.getByText('Rights')).toBeInTheDocument()
    expect(screen.getByText('Users')).toBeInTheDocument()
  })

  it('shows the Rights tab content by default', () => {
    render(
      <Provider store={store}>
        <RoleDetails />
      </Provider>
    )

    expect(screen.getByTestId('role-rights-view')).toBeInTheDocument()
  })

  it('navigates back to roles page when All Roles button is clicked', () => {
    render(
      <Provider store={store}>
        <RoleDetails />
      </Provider>
    )

    const allRolesButton = screen.getByText('All Roles')
    fireEvent.click(allRolesButton)

    expect(mockPush).toHaveBeenCalledWith('/roles')
  })
})
