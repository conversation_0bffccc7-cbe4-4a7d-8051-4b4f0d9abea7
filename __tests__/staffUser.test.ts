import {
  activateUser,
  approveCreateUser,
  approveUpdateUser,
  assignUserLoanProducts,
  changeUserStatus,
  createUser,
  deactivateUser,
  //   FileFormat,
  generateUserReports,
  getUserADProfile,
  getUserById,
  getUsers,
  getUsersByRoleId,
  makeCreateUser,
  makeUpdateUser,
  rejectCreateUser,
  rejectUpdateUser,
  updateUser,
} from '@/store/actions'
import { ILocalNotification, setNotification } from '@dtbx/store/reducers'

import {
  setGeneratedUserReportFailure,
  setGeneratedUserReportLoading,
  setGeneratedUserReportSuccess,
  setIsLoadingCreateUser,
  setIsLoadingEditUser,
  setIsLoadingUsers,
  setLoadingADFailure,
  setLoadingADSuccess,
  setLoadingADUserDetails,
  setRoleUsers,
  setSingleUserData,
  setUsersResponse,
} from '@/store/reducers'
import { describe, it, vi, expect, beforeEach } from 'vitest'
import {
  usersResponseStub,
  userStub,
  createUserStub,
  updateUserStub,
} from './stubs/userStubs'

// Mock the secureapi module
vi.mock('@dtbx/store/utils', () => ({
  secureapi: {
    get: vi.fn(),
    post: vi.fn(),
    put: vi.fn(),
    delete: vi.fn(),
  },
  secureapi2: {
    get: vi.fn(),
    post: vi.fn(),
    put: vi.fn(),
    delete: vi.fn(),
  },
  downloadBlob: vi.fn(),
}))

import { secureapi, secureapi2 } from '@dtbx/store/utils'

describe('staffUsers', () => {
  // GET USER APIs
  describe('getUsers', () => {
    it('should dispatch loading state before making API call', async () => {
      const dispatch = vi.fn()
      const params = { firstName: 'John' }

      await getUsers(dispatch, params)

      expect(dispatch).toHaveBeenCalledWith(setIsLoadingUsers(true))
    })
    it('should dispatch loading state and construct query parameters correctly', async () => {
      const dispatch = vi.fn()
      const params = {
        firstName: 'John',
        lastName: 'Doe',
        email: '<EMAIL>',
        status: 'active',
        phoneNumber: '1234567890',
        size: 20,
        page: 2,
        roleIds: ['role1', 'role2'],
      }
      secureapi.get = vi.fn().mockResolvedValue({ data: usersResponseStub })

      await getUsers(dispatch, params)

      const expectedParamString = [
        'page=2',
        'size=20',
        'firstName=John',
        'lastName=Doe',
        'email=<EMAIL>',
        'status=active',
        'phoneNumber=1234567890',
        'roleIds=role1&roleIds=role2',
      ]
        .filter(Boolean)
        .join('&')

      expect(dispatch).toHaveBeenCalledWith(setIsLoadingUsers(true))
      expect(secureapi.get).toHaveBeenCalledWith(
        '/backoffice-auth/users?' + expectedParamString
      )

      expect(dispatch).toHaveBeenCalledWith(setUsersResponse(usersResponseStub))
      expect(dispatch).toHaveBeenCalledWith(setIsLoadingUsers(false))
    })
    it('should call secureapi.get with correct URL when params are provided', async () => {
      const dispatch = vi.fn()
      const params = {
        firstName: 'John',
        lastName: 'Doe',
        email: '<EMAIL>',
        status: 'active',
        phoneNumber: '1234567890',
        size: 20,
        page: 2,
        roleIds: ['role1', 'role2'],
      }

      const secureApiGetMock = vi
        .spyOn(secureapi, 'get')
        .mockResolvedValue({ data: usersResponseStub })

      await getUsers(dispatch, params)

      const expectedParamString = [
        'page=2',
        'size=20',
        'firstName=John',
        'lastName=Doe',
        'email=<EMAIL>',
        'status=active',
        'phoneNumber=1234567890',
        'roleIds=role1&roleIds=role2',
      ].join('&')
      const expectedUrl = '/backoffice-auth/users?' + expectedParamString

      expect(secureApiGetMock).toHaveBeenCalledWith(expectedUrl)
    })

    it('should dispatch loading state, make API call, and dispatch response data', async () => {
      const dispatch = vi.fn()
      const params = {
        firstName: 'John',
        lastName: 'Doe',
        email: '<EMAIL>',
        status: 'active',
        phoneNumber: '1234567890',
        size: 10,
        page: 1,
        roleIds: ['role1', 'role2'],
      }

      const mockResponse = { data: usersResponseStub }
      secureapi.get = vi.fn().mockResolvedValue(mockResponse)

      await getUsers(dispatch, params)

      const expectedParamString = [
        'page=1',
        'size=10',
        'firstName=John',
        'lastName=Doe',
        'email=<EMAIL>',
        'status=active',
        'phoneNumber=1234567890',
        'roleIds=role1&roleIds=role2',
      ]
      const expectedUrl =
        '/backoffice-auth/users?' + expectedParamString.join('&')

      expect(dispatch).toHaveBeenCalledWith(setIsLoadingUsers(true))
      expect(secureapi.get).toHaveBeenCalledWith(expectedUrl)
      expect(dispatch).toHaveBeenCalledWith(setUsersResponse(mockResponse.data))
      expect(dispatch).toHaveBeenCalledWith(setIsLoadingUsers(false))
    })

    it('should dispatch loading state off after successful API call', async () => {
      const dispatch = vi.fn()
      const params = {
        firstName: 'John',
        lastName: 'Doe',
        email: '<EMAIL>',
        status: 'active',
        phoneNumber: '1234567890',
        size: 10,
        page: 1,
        roleIds: ['role1', 'role2'],
      }

      const mockRes = { data: usersResponseStub }
      secureapi.get = vi.fn().mockResolvedValue(mockRes)

      await getUsers(dispatch, params)

      const expectedParamString = [
        'page=1',
        'size=10',
        'firstName=John',
        'lastName=Doe',
        'email=<EMAIL>',
        'status=active',
        'phoneNumber=1234567890',
        'roleIds=role1&roleIds=role2',
      ]
      const expectedUrl =
        '/backoffice-auth/users?' + expectedParamString.join('&')

      expect(secureapi.get).toHaveBeenCalledWith(expectedUrl)
      expect(dispatch).toHaveBeenCalledWith(setUsersResponse(usersResponseStub))
      expect(dispatch).toHaveBeenCalledWith(setIsLoadingUsers(false))
    })

    it('should set loading state to false after data dispatch', async () => {
      const dispatch = vi.fn()
      const params = {
        firstName: 'John',
        lastName: 'Doe',
        email: '<EMAIL>',
        status: 'active',
        phoneNumber: '1234567890',
        size: 10,
        page: 1,
        roleIds: ['role1', 'role2'],
      }

      await getUsers(dispatch, params)

      expect(dispatch).toHaveBeenCalledWith(setIsLoadingUsers(true))
      expect(dispatch).toHaveBeenCalledWith(setIsLoadingUsers(false))
    })
    it('should dispatch loading state even if response data is empty', async () => {
      const dispatch = vi.fn()
      const params = {
        firstName: 'John',
        lastName: 'Doe',
        email: '<EMAIL>',
        status: 'active',
        phoneNumber: '1234567890',
        size: 10,
        page: 1,
        roleIds: ['role1', 'role2'],
      }

      const secureApiGetMock = vi
        .spyOn(secureapi, 'get')
        .mockResolvedValue({ data: [] })
      const originalSecureapi = secureapi

      await getUsers(dispatch, params)

      expect(dispatch).toHaveBeenCalledWith(setIsLoadingUsers(true))
      secureApiGetMock.mockRestore()
    })
  })
  describe('getUsersByRoleId', () => {
    const roleId = '123'
    const page = 1
    const size = 10
    const mockData = usersResponseStub
    const errorMessage = 'Network Error'

    beforeEach(() => {
      vi.clearAllMocks()
    })

    it('should dispatch setRoleUsers with data and setIsLoadingUsers with false when API call is successful', async () => {
      const dispatch = vi.fn()
      secureapi.get = vi.fn().mockResolvedValue({ data: mockData })

      await getUsersByRoleId(dispatch, roleId, page, size)

      expect(dispatch).toHaveBeenCalledWith(setIsLoadingUsers(true))
      expect(secureapi.get).toHaveBeenCalledWith(
        `/backoffice-auth/roles/${roleId}/users?page=${page}&size=${size}`
      )
      expect(dispatch).toHaveBeenCalledWith(setRoleUsers(mockData))
      expect(dispatch).toHaveBeenCalledWith(setIsLoadingUsers(false))
    })

    it('should dispatch error notification and setIsLoadingUsers with false when API call fails', async () => {
      const dispatch = vi.fn()
      secureapi.get = vi.fn().mockRejectedValue(new Error(errorMessage))

      await getUsersByRoleId(dispatch, roleId, page, size)

      expect(dispatch).toHaveBeenCalledWith(setIsLoadingUsers(true))
      expect(secureapi.get).toHaveBeenCalledWith(
        `/backoffice-auth/roles/${roleId}/users?page=${page}&size=${size}`
      )
      expect(dispatch).toHaveBeenCalledWith(
        setNotification({ message: errorMessage, type: 'error' })
      )
      expect(dispatch).toHaveBeenCalledWith(setIsLoadingUsers(false))
    })

    it('should not update state when API call fails', async () => {
      const dispatch = vi.fn()
      const errorMessage = 'Failed to fetch data'

      secureapi.get = vi.fn().mockRejectedValue(new Error(errorMessage))

      await getUsersByRoleId(dispatch, roleId, page, size)

      expect(dispatch).toHaveBeenCalledWith(setIsLoadingUsers(true))
      expect(secureapi.get).toHaveBeenCalledWith(
        `/backoffice-auth/roles/${roleId}/users?page=${page}&size=${size}`
      )
      expect(dispatch).toHaveBeenCalledWith(
        setNotification({ message: errorMessage, type: 'error' })
      )
      expect(dispatch).toHaveBeenCalledWith(setIsLoadingUsers(false))
    })
  })
  describe('getUserById', () => {
    const userId = '123'
    const mockUserData = userStub

    beforeEach(() => {
      vi.clearAllMocks()
    })

    it('should dispatch setSingleUserData with user data when user ID exists', async () => {
      const dispatch = vi.fn()
      secureapi.get = vi.fn().mockResolvedValue({ data: mockUserData })

      await getUserById(dispatch, userId)

      expect(dispatch).toHaveBeenCalledWith(setSingleUserData(mockUserData))
    })

    it('should log an error when user ID does not exist', async () => {
      const dispatch = vi.fn()
      const userId = 'nonexistent'
      const consoleErrorSpy = vi
        .spyOn(console, 'error')
        .mockImplementation(() => {})

      secureapi.get = vi.fn().mockRejectedValue(new Error('User not found'))

      await getUserById(dispatch, userId)

      expect(consoleErrorSpy).toHaveBeenCalledWith(
        'Error fetching user by id',
        expect.any(Error)
      )

      consoleErrorSpy.mockRestore()
    })

    it('should not dispatch on API failure', async () => {
      const dispatch = vi.fn()
      const userId = '123'

      secureapi.get = vi.fn().mockRejectedValue('API error')

      await getUserById(dispatch, userId)

      expect(dispatch).not.toHaveBeenCalled()
    })
  })

  //CREATE USER APIs
  describe('createUser', () => {
    const data = createUserStub
    const dispatch = vi.fn()

    it('should dispatch success notification and fetch users when valid data is provided', async () => {
      secureapi.post = vi.fn().mockResolvedValue({})
      const getUsers = vi.fn()

      const createUsersMock = vi.spyOn(secureapi, 'post').mockResolvedValue({})
      await createUser(data, dispatch)
      await getUsers(dispatch, { size: 10, page: 1 })

      expect(dispatch).toHaveBeenCalledWith(setIsLoadingCreateUser(true))
      expect(createUsersMock).toHaveBeenCalledWith(
        '/backoffice-auth/users',
        data
      )
      expect(dispatch).toHaveBeenCalledWith(setIsLoadingCreateUser(false))
      expect(dispatch).toHaveBeenCalledWith(
        setNotification({
          message: 'User was Successfully Created',
          type: 'success',
        })
      )
      expect(getUsers).toHaveBeenCalledWith(dispatch, { size: 10, page: 1 })
    })

    it('should dispatch success notification after user creation', async () => {
      secureapi.post = vi.fn().mockResolvedValue({})
      const getUsers = vi.fn()

      const createUserMock = vi.spyOn(secureapi, 'post').mockResolvedValue({})

      await createUser(data, dispatch)
      await getUsers(dispatch, { size: 10, page: 1 })

      expect(dispatch).toHaveBeenCalledWith(setIsLoadingCreateUser(true))
      expect(createUserMock).toHaveBeenCalledWith(
        '/backoffice-auth/users',
        data
      )
      expect(dispatch).toHaveBeenCalledWith(setIsLoadingCreateUser(false))
      expect(dispatch).toHaveBeenCalledWith(
        setNotification({
          message: 'User was Successfully Created',
          type: 'success',
        })
      )
      expect(getUsers).toHaveBeenCalledWith(dispatch, { size: 10, page: 1 })
    })

    it('should call getUsers after creating a user', async () => {
      secureapi.post = vi.fn().mockResolvedValue({})
      const getUsers = vi.fn()

      await createUser(data, dispatch)
      await getUsers(dispatch, { size: 10, page: 1 })

      expect(dispatch).toHaveBeenCalledWith(setIsLoadingCreateUser(true))
      expect(secureapi.post).toHaveBeenCalledWith(
        '/backoffice-auth/users',
        data
      )
      expect(dispatch).toHaveBeenCalledWith(setIsLoadingCreateUser(false))
      expect(dispatch).toHaveBeenCalledWith(
        setNotification({
          message: 'User was Successfully Created',
          type: 'success',
        })
      )
      expect(getUsers).toHaveBeenCalledWith(dispatch, { size: 10, page: 1 })
    })

    it('should dispatch error notification when network failure occurs', async () => {
      const errorMessage = 'Network Error'
      secureapi.post = vi.fn().mockRejectedValue(new Error(errorMessage))

      await createUser(data, dispatch)

      expect(dispatch).toHaveBeenCalledWith(setIsLoadingCreateUser(true))
      expect(secureapi.post).toHaveBeenCalledWith(
        '/backoffice-auth/users',
        data
      )
      expect(dispatch).toHaveBeenCalledWith(
        setNotification({
          message: errorMessage,
          type: 'error',
        })
      )
      expect(dispatch).toHaveBeenCalledWith(setIsLoadingCreateUser(false))
    })
  })
  describe('makeCreateUser', () => {
    const mockDispatch = vi.fn()
    const mockData = createUserStub
    it('should dispatch success notification when user is created successfully', async () => {
      secureapi.post = vi.fn().mockResolvedValue({})

      await makeCreateUser(mockData, mockDispatch)

      expect(mockDispatch).toHaveBeenCalledWith(setIsLoadingCreateUser(true))
      expect(secureapi.post).toHaveBeenCalledWith(
        '/backoffice-auth/users/make',
        mockData
      )
      expect(mockDispatch).toHaveBeenCalledWith(setIsLoadingCreateUser(false))
      expect(mockDispatch).toHaveBeenCalledWith(
        setNotification({
          message: 'User was Successfully Created awaiting approval',
          type: 'success',
        })
      )
    })
    it('should dispatch loading state changes in the correct sequence', async () => {
      secureapi.post = vi.fn().mockResolvedValue({})

      await makeCreateUser(mockData, mockDispatch)

      expect(mockDispatch).toHaveBeenCalledWith(setIsLoadingCreateUser(true))
      expect(secureapi.post).toHaveBeenCalledWith(
        '/backoffice-auth/users/make',
        mockData
      )
      expect(mockDispatch).toHaveBeenCalledWith(setIsLoadingCreateUser(false))
      expect(mockDispatch).toHaveBeenCalledWith(
        setNotification({
          message: 'User was Successfully Created awaiting approval',
          type: 'success',
        })
      )
    })

    it('should dispatch error notification when API call fails', async () => {
      const mockError = new Error('API call failed')
      secureapi.post = vi.fn().mockRejectedValue(mockError)

      await makeCreateUser(mockData, mockDispatch)

      expect(mockDispatch).toHaveBeenCalledWith(setIsLoadingCreateUser(true))
      expect(secureapi.post).toHaveBeenCalledWith(
        '/backoffice-auth/users/make',
        mockData
      )
      expect(mockDispatch).toHaveBeenCalledWith(
        setNotification({
          message: 'API call failed',
          type: 'error',
        })
      )
      expect(mockDispatch).toHaveBeenCalledWith(setIsLoadingCreateUser(false))
    })
    it('should dispatch error notification when network failure occurs', async () => {
      const errorMessage = 'Network Error'
      secureapi.post = vi.fn().mockRejectedValue(new Error(errorMessage))

      await makeCreateUser(mockData, mockDispatch)

      expect(mockDispatch).toHaveBeenCalledWith(setIsLoadingCreateUser(true))
      expect(secureapi.post).toHaveBeenCalledWith(
        '/backoffice-auth/users/make',
        mockData
      )
      expect(mockDispatch).toHaveBeenCalledWith(
        setNotification({
          message: errorMessage,
          type: 'error',
        })
      )
      expect(mockDispatch).toHaveBeenCalledWith(setIsLoadingCreateUser(false))
    })
  })
  describe('approveCreateUser', () => {
    const dispatch = vi.fn()
    const approvalId = '123'
    const comments = 'Approved'
    const getApprovals = vi.fn()

    it('should dispatch success notification and update approvals when API call is successful', async () => {
      secureapi.put = vi.fn().mockResolvedValue({})

      const mockApprove = vi.spyOn(secureapi, 'put').mockResolvedValue({})
      await approveCreateUser(approvalId, comments, dispatch)
      await getApprovals(dispatch)

      expect(dispatch).toHaveBeenCalledWith(setIsLoadingEditUser(true))
      expect(mockApprove).toHaveBeenCalledWith(
        `/backoffice-auth/users/approve/${approvalId}`,
        { comments }
      )
      expect(dispatch).toHaveBeenCalledWith(setIsLoadingEditUser(false))
      expect(dispatch).toHaveBeenCalledWith(
        setNotification({
          message: 'Create staff user request has been approved.',
          type: 'success',
        })
      )
      expect(getApprovals).toHaveBeenCalledWith(dispatch)
    })

    it('should dispatch success notification on approval', async () => {
      const mockApprove = vi.spyOn(secureapi, 'put').mockResolvedValue({})
      await approveCreateUser(approvalId, comments, dispatch)
      await getApprovals(dispatch)

      expect(dispatch).toHaveBeenCalledWith(setIsLoadingEditUser(true))
      expect(secureapi.put).toHaveBeenCalledWith(
        `/backoffice-auth/users/approve/${approvalId}`,
        { comments }
      )
      expect(dispatch).toHaveBeenCalledWith(setIsLoadingEditUser(false))
      expect(dispatch).toHaveBeenCalledWith(
        setNotification({
          message: 'Create staff user request has been approved.',
          type: 'success',
        })
      )
      expect(getApprovals).toHaveBeenCalledWith(dispatch)
    })

    it('should call getApprovals after successful approval', async () => {
      const dispatch = vi.fn()
      const approvalId = '123'
      const comments = 'Approved'

      secureapi.put = vi.fn().mockResolvedValue({})

      await approveCreateUser(approvalId, comments, dispatch)
      await getApprovals(dispatch)

      expect(dispatch).toHaveBeenCalledWith(setIsLoadingEditUser(true))
      expect(secureapi.put).toHaveBeenCalledWith(
        `/backoffice-auth/users/approve/${approvalId}`,
        { comments }
      )
      expect(dispatch).toHaveBeenCalledWith(setIsLoadingEditUser(false))
      expect(dispatch).toHaveBeenCalledWith(
        setNotification({
          message: 'Create staff user request has been approved.',
          type: 'success',
        })
      )
      expect(getApprovals).toHaveBeenCalledWith(dispatch)
    })

    it('should set loading state correctly when API call is successful', async () => {
      const dispatch = vi.fn()
      const approvalId = '123'
      const comments = 'Approved'

      secureapi.put = vi.fn().mockResolvedValue({})

      await approveCreateUser(approvalId, comments, dispatch)
      await getApprovals(dispatch)

      expect(dispatch).toHaveBeenCalledWith(setIsLoadingEditUser(true))
      expect(secureapi.put).toHaveBeenCalledWith(
        `/backoffice-auth/users/approve/${approvalId}`,
        { comments }
      )
      expect(dispatch).toHaveBeenCalledWith(setIsLoadingEditUser(false))
      expect(dispatch).toHaveBeenCalledWith(
        setNotification({
          message: 'Create staff user request has been approved.',
          type: 'success',
        })
      )
      expect(getApprovals).toHaveBeenCalledWith(dispatch)
    })

    it('should dispatch error notification when API call fails', async () => {
      const dispatch = vi.fn()
      const approvalId = '123'
      const comments = 'Approved'
      const errorMessage = 'Network Error'

      secureapi.put = vi.fn().mockRejectedValue(new Error(errorMessage))

      await approveCreateUser(approvalId, comments, dispatch)

      expect(dispatch).toHaveBeenCalledWith(setIsLoadingEditUser(true))
      expect(secureapi.put).toHaveBeenCalledWith(
        `/backoffice-auth/users/approve/${approvalId}`,
        { comments }
      )
      expect(dispatch).toHaveBeenCalledWith(
        setNotification({
          message: errorMessage,
          type: 'error',
        })
      )
      expect(dispatch).toHaveBeenCalledWith(setIsLoadingEditUser(false))
    })
  })

  // REJECT USER APIs
  describe('rejectCreateUser', () => {
    const dispatch = vi.fn()
    const approvalId = 'validApprovalId'
    const comments = 'Valid comments'
    const getApprovals = vi.fn()
    it('should dispatch success notification when rejection is successful', async () => {
      secureapi.put = vi.fn().mockResolvedValue({})
      const mockUpdateAxiosHandler = vi
        .spyOn(secureapi, 'put')
        .mockResolvedValue({})
      await getApprovals(dispatch)

      await rejectCreateUser(approvalId, comments, dispatch)

      expect(dispatch).toHaveBeenCalledWith(setIsLoadingEditUser(true))
      expect(mockUpdateAxiosHandler).toHaveBeenCalledWith(
        `/backoffice-auth/users/reject/${approvalId}`,
        { comments }
      )
      expect(dispatch).toHaveBeenCalledWith(setIsLoadingEditUser(false))
      expect(dispatch).toHaveBeenCalledWith(
        setNotification({
          message: 'Create staff user request has been rejected.',
          type: 'success',
        })
      )
      expect(getApprovals).toHaveBeenCalledWith(dispatch)
    })

    it('should call getApprovals after rejection', async () => {
      const mockUpdateAxiosHandler = vi
        .spyOn(secureapi, 'put')
        .mockResolvedValue({})

      await rejectCreateUser(approvalId, comments, dispatch)
      await getApprovals(dispatch)

      expect(dispatch).toHaveBeenCalledWith(setIsLoadingEditUser(true))
      expect(mockUpdateAxiosHandler).toHaveBeenCalledWith(
        `/backoffice-auth/users/reject/${approvalId}`,
        { comments }
      )
      expect(dispatch).toHaveBeenCalledWith(setIsLoadingEditUser(false))
      expect(dispatch).toHaveBeenCalledWith(
        setNotification({
          message: 'Create staff user request has been rejected.',
          type: 'success',
        })
      )
      expect(getApprovals).toHaveBeenCalledWith(dispatch)
    })

    it('should dispatch error notification when rejection fails', async () => {
      const mockErrorMessage = 'Failed to reject'
      const mockFailedUpdateAxiosHandler = vi
        .spyOn(secureapi, 'put')
        .mockRejectedValue(new Error(mockErrorMessage))

      await rejectCreateUser(approvalId, comments, dispatch)
      await getApprovals(dispatch)

      expect(dispatch).toHaveBeenCalledWith(setIsLoadingEditUser(true))
      expect(mockFailedUpdateAxiosHandler).toHaveBeenCalledWith(
        `/backoffice-auth/users/reject/${approvalId}`,
        { comments }
      )
      expect(dispatch).toHaveBeenCalledWith(setIsLoadingEditUser(false))
      expect(dispatch).toHaveBeenCalledWith(
        setNotification({
          message: mockErrorMessage,
          type: 'error',
        })
      )
      expect(dispatch).toHaveBeenCalledWith(setIsLoadingEditUser(false))
    })

    it('should handle empty comments without crashing', async () => {
      const dispatch = vi.fn()
      const approvalId = 'validApprovalId'
      const comments = ''

      const mockUpdateAxiosHandler = vi
        .spyOn(secureapi, 'put')
        .mockResolvedValueOnce({})

      await rejectCreateUser(approvalId, comments, dispatch)
      await getApprovals(dispatch)

      expect(dispatch).toHaveBeenCalledWith(setIsLoadingEditUser(true))
      expect(mockUpdateAxiosHandler).toHaveBeenCalledWith(
        `/backoffice-auth/users/reject/${approvalId}`,
        { comments }
      )
      expect(dispatch).toHaveBeenCalledWith(setIsLoadingEditUser(false))
      expect(dispatch).toHaveBeenCalledWith(
        setNotification({
          message: 'Create staff user request has been rejected.',
          type: 'success',
        })
      )
      expect(getApprovals).toHaveBeenCalledWith(dispatch)
    })

    it('should dispatch error notification when API call fails', async () => {
      const approvalId = 'validApprovalId'
      const comments = 'Valid comments'
      const errorMessage = 'Network Error'

      const mockUpdateAxiosErrorHandler = vi
        .spyOn(secureapi, 'put')
        .mockRejectedValue(new Error(errorMessage))

      await rejectCreateUser(approvalId, comments, dispatch)

      expect(dispatch).toHaveBeenCalledWith(setIsLoadingEditUser(true))
      expect(mockUpdateAxiosErrorHandler).toHaveBeenCalledWith(
        `/backoffice-auth/users/reject/${approvalId}`,
        { comments }
      )
      expect(dispatch).toHaveBeenCalledWith(
        setNotification({
          message: errorMessage,
          type: 'error',
        })
      )
      expect(dispatch).toHaveBeenCalledWith(setIsLoadingEditUser(false))
    })
  })

  //UPDATE USER APIs
  describe('updateUser', () => {
    const dispatch = vi.fn()
    const getUsers = vi.fn()
    const data = updateUserStub
    const userId = '123'
    const refreshToken = vi.fn()
    it('should update user data and dispatch success notification when valid userId and data are provided', async () => {
      const isActiveUserRoleUpdate = false

      const mockUpdateAxiosHandler = vi
        .spyOn(secureapi, 'put')
        .mockResolvedValueOnce({})

      await updateUser(userId, data, dispatch, isActiveUserRoleUpdate)
      await getUsers(dispatch, { page: 1, size: 10 })

      expect(dispatch).toHaveBeenCalledWith(setIsLoadingEditUser(true))
      expect(mockUpdateAxiosHandler).toHaveBeenCalledWith(
        `/backoffice-auth/users/${userId}`,
        data
      )
      expect(dispatch).toHaveBeenCalledWith(setIsLoadingEditUser(false))
      expect(dispatch).toHaveBeenCalledWith(
        setNotification({
          message: 'User was Successfully Updated',
          type: 'success',
        })
      )
      expect(getUsers).toHaveBeenCalledWith(dispatch, { page: 1, size: 10 })
    })
    it('should dispatch success notification after user update', async () => {
      const isActiveUserRoleUpdate = false

      secureapi.put = vi.fn().mockResolvedValue({})

      await updateUser(userId, data, dispatch, isActiveUserRoleUpdate)
      await getUsers(dispatch, { page: 1, size: 10 })

      expect(dispatch).toHaveBeenCalledWith(setIsLoadingEditUser(true))
      expect(secureapi.put).toHaveBeenCalledWith(
        `/backoffice-auth/users/${userId}`,
        data
      )
      expect(dispatch).toHaveBeenCalledWith(setIsLoadingEditUser(false))
      expect(dispatch).toHaveBeenCalledWith(
        setNotification({
          message: 'User was Successfully Updated',
          type: 'success',
        })
      )
      expect(getUsers).toHaveBeenCalledWith(dispatch, { page: 1, size: 10 })
    })

    it('should refresh token when isActiveUserRoleUpdate is true', async () => {
      const isActiveUserRoleUpdate = true

      secureapi.put = vi.fn().mockResolvedValue({})

      await updateUser(userId, data, dispatch, isActiveUserRoleUpdate)
      await getUsers(dispatch, { page: 1, size: 10 })
      await refreshToken()

      expect(dispatch).toHaveBeenCalledWith(setIsLoadingEditUser(true))
      expect(secureapi.put).toHaveBeenCalledWith(
        `/backoffice-auth/users/${userId}`,
        data
      )
      expect(dispatch).toHaveBeenCalledWith(setIsLoadingEditUser(false))
      expect(refreshToken).toHaveBeenCalled()
      expect(dispatch).toHaveBeenCalledWith(
        setNotification({
          message: 'User was Successfully Updated',
          type: 'success',
        })
      )
      expect(getUsers).toHaveBeenCalledWith(dispatch, {
        page: 1,
        size: 10,
      })
    })
    it('should update user data and dispatch success notification when valid userId and data are provided', async () => {
      const dispatch = vi.fn()
      const userId = '123'
      const isActiveUserRoleUpdate = false

      secureapi.put = vi.fn().mockResolvedValue({})

      await updateUser(userId, data, dispatch, isActiveUserRoleUpdate)
      await getUsers(dispatch, { page: 1, size: 10 })

      expect(dispatch).toHaveBeenCalledWith(setIsLoadingEditUser(true))
      expect(secureapi.put).toHaveBeenCalledWith(
        `/backoffice-auth/users/${userId}`,
        data
      )
      expect(dispatch).toHaveBeenCalledWith(setIsLoadingEditUser(false))
      expect(dispatch).toHaveBeenCalledWith(
        setNotification({
          message: 'User was Successfully Updated',
          type: 'success',
        })
      )
      expect(getUsers).toHaveBeenCalledWith(dispatch, {
        page: 1,
        size: 10,
      })
    })
    it('should stop loading indicator after successful update', async () => {
      const isActiveUserRoleUpdate = false

      const mockUpdateAxiosHandler = vi
        .spyOn(secureapi, 'put')
        .mockResolvedValue({})

      await updateUser(userId, data, dispatch, isActiveUserRoleUpdate)
      await getUsers(dispatch, { page: 1, size: 10 })

      expect(dispatch).toHaveBeenCalledWith(setIsLoadingEditUser(true))
      expect(mockUpdateAxiosHandler).toHaveBeenCalledWith(
        `/backoffice-auth/users/${userId}`,
        data
      )
      expect(dispatch).toHaveBeenCalledWith(setIsLoadingEditUser(false))
      expect(dispatch).toHaveBeenCalledWith(
        setNotification({
          message: 'User was Successfully Updated',
          type: 'success',
        })
      )
      expect(getUsers).toHaveBeenCalledWith(dispatch, {
        page: 1,
        size: 10,
      })
    })
    it('should dispatch error notification when secureapi.put fails', async () => {
      const isActiveUserRoleUpdate = false

      const errorMessage = 'Network Error'
      const mockUpdateAxiosErrorHandler = vi
        .spyOn(secureapi, 'put')
        .mockRejectedValue(new Error(errorMessage))

      await updateUser(userId, data, dispatch, isActiveUserRoleUpdate)

      expect(dispatch).toHaveBeenCalledWith(setIsLoadingEditUser(true))
      expect(mockUpdateAxiosErrorHandler).toHaveBeenCalledWith(
        `/backoffice-auth/users/${userId}`,
        data
      )
      expect(dispatch).toHaveBeenCalledWith(
        setNotification({
          message: errorMessage,
          type: 'error',
        })
      )
      expect(dispatch).toHaveBeenCalledWith(setIsLoadingEditUser(false))
    })
    it('should dispatch error notification when update fails', async () => {
      const isActiveUserRoleUpdate = false

      const mockUpdateAxiosErrorHandler = vi
        .spyOn(secureapi, 'put')
        .mockRejectedValue(new Error('Update failed'))

      await updateUser(userId, data, dispatch, isActiveUserRoleUpdate)

      expect(dispatch).toHaveBeenCalledWith(setIsLoadingEditUser(true))
      expect(mockUpdateAxiosErrorHandler).toHaveBeenCalledWith(
        `/backoffice-auth/users/${userId}`,
        data
      )
      expect(dispatch).toHaveBeenCalledWith(setIsLoadingEditUser(false))
      expect(dispatch).toHaveBeenCalledWith(
        setNotification({
          message: 'Update failed',
          type: 'error',
        })
      )
    })
  })
  describe('makeUpdateUser', () => {
    it('should dispatch success notification when user data is successfully updated', async () => {
      const mockDispatch = vi.fn()
      const mockUserId = '123'
      const mockData = updateUserStub
      secureapi.put = vi.fn().mockResolvedValue({})
      const getUsers = vi.fn()

      await makeUpdateUser(mockUserId, mockData, mockDispatch)
      await getUsers(mockDispatch, {
        page: 1,
        size: 10,
      })

      expect(mockDispatch).toHaveBeenCalledWith(setIsLoadingEditUser(true))
      expect(secureapi.put).toHaveBeenCalledWith(
        `/backoffice-auth/users/${mockUserId}/make`,
        mockData
      )
      expect(mockDispatch).toHaveBeenCalledWith(setIsLoadingEditUser(false))
      expect(mockDispatch).toHaveBeenCalledWith(
        setNotification({
          message: 'User was Successfully Updated pending approval',
          type: 'success',
        })
      )
      expect(getUsers).toHaveBeenCalledWith(mockDispatch, {
        page: 1,
        size: 10,
      })
    })
    it('should invoke getUsers with default pagination after successful update', async () => {
      const mockDispatch = vi.fn()
      const mockUserId = '123'
      const mockData = updateUserStub
      secureapi.put = vi.fn().mockResolvedValue({})
      const getUsers = vi.fn()

      await makeUpdateUser(mockUserId, mockData, mockDispatch)
      await getUsers(mockDispatch, {
        page: 1,
        size: 10,
      })

      expect(mockDispatch).toHaveBeenCalledWith(setIsLoadingEditUser(true))
      expect(secureapi.put).toHaveBeenCalledWith(
        `/backoffice-auth/users/${mockUserId}/make`,
        mockData
      )
      expect(mockDispatch).toHaveBeenCalledWith(setIsLoadingEditUser(false))
      expect(mockDispatch).toHaveBeenCalledWith(
        setNotification({
          message: 'User was Successfully Updated pending approval',
          type: 'success',
        })
      )
      expect(getUsers).toHaveBeenCalledWith(mockDispatch, {
        page: 1,
        size: 10,
      })
    })
    it('should dispatch error notification when API call fails', async () => {
      const mockDispatch = vi.fn()
      const mockUserId = '123'
      const mockData = updateUserStub
      const errorMessage = 'API Error'
      secureapi.put = vi.fn().mockRejectedValue(new Error(errorMessage))

      await makeUpdateUser(mockUserId, mockData, mockDispatch)

      expect(mockDispatch).toHaveBeenCalledWith(setIsLoadingEditUser(true))
      expect(secureapi.put).toHaveBeenCalledWith(
        `/backoffice-auth/users/${mockUserId}/make`,
        mockData
      )
      expect(mockDispatch).toHaveBeenCalledWith(
        setNotification({
          message: errorMessage,
          type: 'error',
        })
      )
      expect(mockDispatch).toHaveBeenCalledWith(setIsLoadingEditUser(false))
    })
    it('should not make additional API calls on failure', async () => {
      const mockDispatch = vi.fn()
      const mockUserId = '123'
      const mockData = updateUserStub
      const mockErrorMessage = 'Failed to update user'

      const mockAxiosErrorHandler = vi
        .spyOn(secureapi, 'put')
        .mockRejectedValue(new Error('Failed to update user'))
      const expectedNotification: ILocalNotification = {
        message: mockErrorMessage,
        type: 'error' as any,
      }

      await makeUpdateUser(mockUserId, mockData, mockDispatch)

      expect(mockDispatch).toHaveBeenCalledWith(setIsLoadingEditUser(true))
      expect(mockAxiosErrorHandler).toHaveBeenCalledWith(
        `/backoffice-auth/users/${mockUserId}/make`,
        mockData
      )
      expect(mockDispatch).toHaveBeenCalledWith(
        setNotification(expectedNotification)
      )
      expect(mockDispatch).toHaveBeenCalledWith(setIsLoadingEditUser(false))
    })
  })
  describe('approveUpdateUser', () => {
    it('should dispatch success notification and reload users when update is approved', async () => {
      const dispatch = vi.fn()
      const userId = '123'
      const data = {
        comments: 'test comment',
      }

      secureapi.put = vi.fn().mockResolvedValue({})
      const getUsers = vi.fn()

      await approveUpdateUser(userId, data, dispatch)
      await getUsers(dispatch, { page: 1, size: 10 })

      expect(dispatch).toHaveBeenCalledWith(setIsLoadingEditUser(true))
      expect(secureapi.put).toHaveBeenCalledWith(
        `/backoffice-auth/users/${userId}/approve`,
        data
      )
      expect(dispatch).toHaveBeenCalledWith(setIsLoadingEditUser(false))
      expect(dispatch).toHaveBeenCalledWith(
        setNotification({
          message: 'User update was successfully approved',
          type: 'success',
        })
      )
      expect(getUsers).toHaveBeenCalledWith(dispatch, { page: 1, size: 10 })
    })

    it('should dispatch loading state and success notification when update is approved', async () => {
      const dispatch = vi.fn()
      const userId = '123'
      const getUsers = vi.fn()
      const data = {
        comments: 'test comment',
      }

      const mockUpdateAxiosHandler = vi
        .spyOn(secureapi, 'put')
        .mockResolvedValue({})

      await approveUpdateUser(userId, data, dispatch)
      await getUsers(dispatch, { page: 1, size: 10 })

      expect(dispatch).toHaveBeenCalledWith(setIsLoadingEditUser(true))
      expect(mockUpdateAxiosHandler).toHaveBeenCalledWith(
        `/backoffice-auth/users/${userId}/approve`,
        data
      )
      expect(dispatch).toHaveBeenCalledWith(setIsLoadingEditUser(false))
      expect(dispatch).toHaveBeenCalledWith(
        setNotification({
          message: 'User update was successfully approved',
          type: 'success',
        })
      )
      expect(getUsers).toHaveBeenCalledWith(dispatch, { page: 1, size: 10 })
    })

    it('should dispatch success notification and reload users when update is approved', async () => {
      const dispatch = vi.fn()
      const userId = '123'
      const data = {
        comments: 'test comment',
      }
      const getUsers = vi.fn()

      const mockAxiosUpdateHandler = vi
        .spyOn(secureapi, 'put')
        .mockResolvedValue({})

      await approveUpdateUser(userId, data, dispatch)
      await getUsers(dispatch, { page: 1, size: 10 })

      expect(dispatch).toHaveBeenCalledWith(setIsLoadingEditUser(true))
      expect(mockAxiosUpdateHandler).toHaveBeenCalledWith(
        `/backoffice-auth/users/${userId}/approve`,
        data
      )
      expect(dispatch).toHaveBeenCalledWith(setIsLoadingEditUser(false))
      expect(dispatch).toHaveBeenCalledWith(
        setNotification({
          message: 'User update was successfully approved',
          type: 'success',
        })
      )
      expect(getUsers).toHaveBeenCalledWith(dispatch, { page: 1, size: 10 })
    })

    it('should dispatch success notification and reload users when update is approved', async () => {
      const dispatch = vi.fn()
      const userId = '123'
      const data = {
        comments: 'test comment',
      }
      const getUsers = vi.fn()

      const mockAxiosUpdateHandler = vi
        .spyOn(secureapi, 'put')
        .mockResolvedValue({})

      await approveUpdateUser(userId, data, dispatch)
      await getUsers(dispatch, { page: 1, size: 10 })

      expect(dispatch).toHaveBeenCalledWith(setIsLoadingEditUser(true))
      expect(secureapi.put).toHaveBeenCalledWith(
        `/backoffice-auth/users/${userId}/approve`,
        data
      )
      expect(dispatch).toHaveBeenCalledWith(setIsLoadingEditUser(false))
      expect(dispatch).toHaveBeenCalledWith(
        setNotification({
          message: 'User update was successfully approved',
          type: 'success',
        })
      )
      expect(getUsers).toHaveBeenCalledWith(dispatch, {
        page: 1,
        size: 10,
      })
    })

    it('should handle concurrent API requests efficiently', async () => {
      const dispatch = vi.fn()
      const userId = '123'
      const data = {
        comments: 'test comment',
      }
      const getUsers = vi.fn()

      const mockAxiosUpdateHandler = vi
        .spyOn(secureapi, 'put')
        .mockResolvedValue({})

      await approveUpdateUser(userId, data, dispatch)
      await getUsers(dispatch, { page: 1, size: 10 })

      expect(dispatch).toHaveBeenCalledWith(setIsLoadingEditUser(true))
      expect(mockAxiosUpdateHandler).toHaveBeenCalledWith(
        `/backoffice-auth/users/${userId}/approve`,
        data
      )
      expect(dispatch).toHaveBeenCalledWith(setIsLoadingEditUser(false))
      expect(dispatch).toHaveBeenCalledWith(
        setNotification({
          message: 'User update was successfully approved',
          type: 'success',
        })
      )
      expect(getUsers).toHaveBeenCalledWith(dispatch, {
        page: 1,
        size: 10,
      })
    })
    it('should dispatch error notification when API call fails', async () => {
      const dispatch = vi.fn()
      const userId = '123'
      const data = {
        comments: 'test comment',
      }
      const errorMessage = 'Network Error'

      const mockAxiosUpdateErrorHandler = vi
        .spyOn(secureapi, 'put')
        .mockRejectedValue(new Error(errorMessage))

      await approveUpdateUser(userId, data, dispatch)

      expect(dispatch).toHaveBeenCalledWith(setIsLoadingEditUser(true))
      expect(mockAxiosUpdateErrorHandler).toHaveBeenCalledWith(
        `/backoffice-auth/users/${userId}/approve`,
        data
      )
      expect(dispatch).toHaveBeenCalledWith(
        setNotification({
          message: errorMessage,
          type: 'error',
        })
      )
      expect(dispatch).toHaveBeenCalledWith(setIsLoadingEditUser(false))
    })
    it('should reset loading state after API call completion', async () => {
      const dispatch = vi.fn()
      const userId = '123'
      const data = {
        comments: 'test comment',
      }

      secureapi.put = vi.fn().mockResolvedValue({})
      const getUsers = vi.fn()

      await approveUpdateUser(userId, data, dispatch)
      await getUsers(dispatch, { page: 1, size: 10 })

      expect(dispatch).toHaveBeenCalledWith(setIsLoadingEditUser(true))
      expect(secureapi.put).toHaveBeenCalledWith(
        `/backoffice-auth/users/${userId}/approve`,
        data
      )
      expect(dispatch).toHaveBeenCalledWith(setIsLoadingEditUser(false))
      expect(dispatch).toHaveBeenCalledWith(
        setNotification({
          message: 'User update was successfully approved',
          type: 'success',
        })
      )
      expect(getUsers).toHaveBeenCalledWith(dispatch, { page: 1, size: 10 })
    })
    it('should dispatch success notification and reload users when update is approved', async () => {
      const dispatch = vi.fn()
      const userId = '123'
      const getUsers = vi.fn()
      const data = {
        comments: 'test comment',
      }

      const mockAxiosUpdateHandler = vi
        .spyOn(secureapi, 'put')
        .mockResolvedValue({})

      await approveUpdateUser(userId, data, dispatch)
      await getUsers(dispatch, { page: 1, size: 10 })

      expect(dispatch).toHaveBeenCalledWith(setIsLoadingEditUser(true))
      expect(mockAxiosUpdateHandler).toHaveBeenCalledWith(
        `/backoffice-auth/users/${userId}/approve`,
        data
      )
      expect(dispatch).toHaveBeenCalledWith(setIsLoadingEditUser(false))
      expect(dispatch).toHaveBeenCalledWith(
        setNotification({
          message: 'User update was successfully approved',
          type: 'success',
        })
      )
      expect(getUsers).toHaveBeenCalledWith(dispatch, { page: 1, size: 10 })
    })
  })
  describe('rejectUpdateUser', () => {
    const dispatch = vi.fn()
    const getUsers = vi.fn()
    const userId = '123'
    const data = { comments: ' reject user update' }

    it('should dispatch success notification when user update is successfully rejected', async () => {
      secureapi.put = vi.fn().mockResolvedValue({})
      const mockUpdateAxiosHandler = vi
        .spyOn(secureapi, 'put')
        .mockResolvedValue({})

      await rejectUpdateUser(userId, data, dispatch)
      await getUsers(dispatch, { page: 1, size: 10 })

      expect(dispatch).toHaveBeenCalledWith(setIsLoadingEditUser(true))
      expect(mockUpdateAxiosHandler).toHaveBeenCalledWith(
        `/backoffice-auth/users/${userId}/reject`,
        data
      )
      expect(dispatch).toHaveBeenCalledWith(setIsLoadingEditUser(false))
      expect(dispatch).toHaveBeenCalledWith(
        setNotification({
          message: 'User update was successfully rejected',
          type: 'success',
        })
      )
      expect(getUsers).toHaveBeenCalledWith(dispatch, { page: 1, size: 10 })
    })

    it('should call secureapi.put with correct endpoint and data and dispatch success notification', async () => {
      const mockUpdateAxiosHandler = vi
        .spyOn(secureapi, 'put')
        .mockResolvedValue({})

      await rejectUpdateUser(userId, data, dispatch)
      await getUsers(dispatch, { page: 1, size: 10 })

      expect(dispatch).toHaveBeenCalledWith(setIsLoadingEditUser(true))
      expect(mockUpdateAxiosHandler).toHaveBeenCalledWith(
        `/backoffice-auth/users/${userId}/reject`,
        data
      )
      expect(dispatch).toHaveBeenCalledWith(setIsLoadingEditUser(false))
      expect(dispatch).toHaveBeenCalledWith(
        setNotification({
          message: 'User update was successfully rejected',
          type: 'success',
        })
      )
      expect(getUsers).toHaveBeenCalledWith(dispatch, {
        page: 1,
        size: 10,
      })
    })
    it('should dispatch error notification when API call fails', async () => {
      const errorMessage = 'API Error'

      const mockUpdateAxiosErrorHandler = vi
        .spyOn(secureapi, 'put')
        .mockRejectedValue(new Error(errorMessage))

      await rejectUpdateUser(userId, data, dispatch)

      expect(dispatch).toHaveBeenCalledWith(setIsLoadingEditUser(true))
      expect(mockUpdateAxiosErrorHandler).toHaveBeenCalledWith(
        `/backoffice-auth/users/${userId}/reject`,
        data
      )
      expect(dispatch).toHaveBeenCalledWith(
        setNotification({
          message: errorMessage,
          type: 'error',
        })
      )
      expect(dispatch).toHaveBeenCalledWith(setIsLoadingEditUser(false))
    })

    it('should not make duplicate API calls when rejecting user update', async () => {
      const mockUpdateAxiosHandler = vi
        .spyOn(secureapi, 'put')
        .mockRejectedValue(new Error('Mock error message'))

      await rejectUpdateUser(userId, data, dispatch)
      await getUsers(dispatch, { page: 1, size: 10 })

      expect(dispatch).toHaveBeenCalledWith(setIsLoadingEditUser(true))
      expect(mockUpdateAxiosHandler).toHaveBeenCalledWith(
        `/backoffice-auth/users/${userId}/reject`,
        data
      )
      expect(dispatch).toHaveBeenCalledWith(setIsLoadingEditUser(false))
      expect(dispatch).toHaveBeenCalledWith(
        setNotification({
          message: 'User update was successfully rejected',
          type: 'success',
        })
      )
      expect(getUsers).toHaveBeenCalledWith(dispatch, {
        page: 1,
        size: 10,
      })
    })
  })

  //ACTIVATE USER APIs
  describe('activateUser', () => {
    it('should dispatch success notification when user is approved', async () => {
      const dispatch = vi.fn()
      const userId = '123'
      const action = 'approve'
      const comments = 'Approved by admin'

      secureapi.put = vi.fn().mockResolvedValue({})
      await activateUser(userId, action, dispatch, comments)

      expect(dispatch).toHaveBeenCalledWith(setIsLoadingEditUser(true))
      expect(secureapi.put).toHaveBeenCalledWith(
        `/backoffice-auth/users/activate/${userId}/approve`,
        { comments }
      )
      expect(dispatch).toHaveBeenCalledWith(setIsLoadingEditUser(false))
      expect(dispatch).toHaveBeenCalledWith(
        setNotification({
          message: 'User activation has been Approved successfully',
          type: 'success',
        })
      )
    })
    it('should dispatch error notification when userId is invalid', async () => {
      const dispatch = vi.fn()
      const userId = 'invalid-id'
      const action = 'approve'
      const comments = 'Attempt to approve'

      const errorMessage = 'User not found'
      secureapi.put = vi.fn().mockRejectedValue(new Error(errorMessage))

      await activateUser(userId, action, dispatch, comments)

      expect(dispatch).toHaveBeenCalledWith(setIsLoadingEditUser(true))
      expect(secureapi.put).toHaveBeenCalledWith(
        `/backoffice-auth/users/activate/${userId}/approve`,
        { comments }
      )
      expect(dispatch).toHaveBeenCalledWith(
        setNotification({
          message: errorMessage,
          type: 'error',
        })
      )
      expect(dispatch).toHaveBeenCalledWith(setIsLoadingEditUser(false))
    })
    it('should dispatch success notification when user is activated with "make" action', async () => {
      const dispatch = vi.fn()
      const userId = '123'
      const action = 'make'
      const comments = 'Activated action'

      secureapi.put = vi.fn().mockResolvedValue({})
      await activateUser(userId, action, dispatch, comments)

      expect(dispatch).toHaveBeenCalledWith(setIsLoadingEditUser(true))
      expect(secureapi.put).toHaveBeenCalledWith(
        `/backoffice-auth/users/activate/${userId}/make`,
        { comments }
      )
      expect(dispatch).toHaveBeenCalledWith(setIsLoadingEditUser(false))
      expect(dispatch).toHaveBeenCalledWith(
        setNotification({
          message: 'User activation has been Initiated successfully',
          type: 'success',
        })
      )
    })
    it('should dispatch success notification when user is rejected', async () => {
      const dispatch = vi.fn()
      const userId = '123'
      const action = 'reject'
      const comments = 'Rejected action'

      secureapi.put = vi.fn().mockResolvedValue({})
      await activateUser(userId, action, dispatch, comments)

      expect(dispatch).toHaveBeenCalledWith(setIsLoadingEditUser(true))
      expect(secureapi.put).toHaveBeenCalledWith(
        `/backoffice-auth/users/activate/${userId}/reject`,
        { comments }
      )
      expect(dispatch).toHaveBeenCalledWith(setIsLoadingEditUser(false))
      expect(dispatch).toHaveBeenCalledWith(
        setNotification({
          message: 'User activation has been Rejected successfully',
          type: 'success',
        })
      )
    })
    it('should construct correct API endpoint URL based on action', async () => {
      const dispatch = vi.fn()
      const userId = '123'
      const action = 'approve'
      const comments = 'Approved action'

      secureapi.put = vi.fn().mockResolvedValue({})
      await activateUser(userId, action, dispatch, comments)

      expect(dispatch).toHaveBeenCalledWith(setIsLoadingEditUser(true))
      expect(secureapi.put).toHaveBeenCalledWith(
        `/backoffice-auth/users/activate/${userId}/approve`,
        { comments }
      )
      expect(dispatch).toHaveBeenCalledWith(setIsLoadingEditUser(false))
      expect(dispatch).toHaveBeenCalledWith(
        setNotification({
          message: 'User activation has been Approved successfully',
          type: 'success',
        })
      )
    })
    it('should dispatch success notification when user is approved', async () => {
      const dispatch = vi.fn()
      const userId = '123'
      const action = 'approve'
      const comments = 'Approved action'

      secureapi.put = vi.fn().mockResolvedValue({})
      await activateUser(userId, action, dispatch, comments)

      expect(dispatch).toHaveBeenCalledWith(setIsLoadingEditUser(true))
      expect(secureapi.put).toHaveBeenCalledWith(
        `/backoffice-auth/users/activate/${userId}/approve`,
        { comments }
      )
      expect(dispatch).toHaveBeenCalledWith(setIsLoadingEditUser(false))
      expect(dispatch).toHaveBeenCalledWith(
        setNotification({
          message: 'User activation has been Approved successfully',
          type: 'success',
        })
      )
    })
  })

  // DEACTIVATE USER APIs

  describe('deactivateUser', () => {
    it('should dispatch success notification when user is deactivated with "approve" action', async () => {
      const dispatch = vi.fn()
      const userId = '123'
      const action = 'approve'
      const comments = 'Deactivating user'

      await deactivateUser(userId, action, dispatch, comments)

      expect(dispatch).toHaveBeenCalledWith(setIsLoadingEditUser(true))
      expect(dispatch).toHaveBeenCalledWith(setIsLoadingEditUser(false))
      expect(dispatch).toHaveBeenCalledWith(
        setNotification({
          message: 'User deactivation has been Approved successfully',
          type: 'success',
        })
      )
    })
    it('should dispatch error notification when userId is invalid', async () => {
      const dispatch = vi.fn()
      const userId = 'invalid-id'
      const action = 'approve'
      const comments = 'Deactivating user'

      secureapi.put = vi.fn().mockRejectedValue(new Error('Invalid userId'))

      await deactivateUser(userId, action, dispatch, comments)

      expect(dispatch).toHaveBeenCalledWith(setIsLoadingEditUser(true))
      expect(dispatch).toHaveBeenCalledWith(
        setNotification({
          message: 'Invalid userId',
          type: 'error',
        })
      )
      expect(dispatch).toHaveBeenCalledWith(setIsLoadingEditUser(false))
    })
  })
  describe('changeUserStatus', () => {
    it('should dispatch success notification when user is activated', async () => {
      const dispatch = vi.fn()
      const userId = '123'
      const action = 'activate'
      const comments = 'Activating user'

      secureapi.put = vi.fn().mockResolvedValue({})
      const getUsers = vi.fn()

      await changeUserStatus(userId, action, dispatch, comments)
      await getUsers(dispatch, { page: 1, size: 10 })

      expect(dispatch).toHaveBeenCalledWith(setIsLoadingEditUser(true))
      expect(secureapi.put).toHaveBeenCalledWith(
        `/backoffice-auth/users/${action}/${userId}`,
        { comments }
      )
      expect(dispatch).toHaveBeenCalledWith(setIsLoadingEditUser(false))
      expect(dispatch).toHaveBeenCalledWith(
        setNotification({
          message: 'User has been Activated successfully',
          type: 'success',
        })
      )
      expect(getUsers).toHaveBeenCalledWith(dispatch, { page: 1, size: 10 })
    })

    it('should dispatch success notification when user is deactivated', async () => {
      const dispatch = vi.fn()
      const userId = '123'
      const action = 'deactivate'
      const comments = 'Deactivating user'

      secureapi.put = vi.fn().mockResolvedValue({})
      const getUsers = vi.fn()

      await changeUserStatus(userId, action, dispatch, comments)
      await getUsers(dispatch, { page: 1, size: 10 })

      expect(dispatch).toHaveBeenCalledWith(setIsLoadingEditUser(true))
      expect(secureapi.put).toHaveBeenCalledWith(
        `/backoffice-auth/users/${action}/${userId}`,
        { comments }
      )
      expect(dispatch).toHaveBeenCalledWith(setIsLoadingEditUser(false))
      expect(dispatch).toHaveBeenCalledWith(
        setNotification({
          message: 'User has been Deactivated successfully',
          type: 'success',
        })
      )
      expect(getUsers).toHaveBeenCalledWith(dispatch, { page: 1, size: 10 })
    })

    it('should dispatch error notification when API call fails', async () => {
      const dispatch = vi.fn()
      const userId = '123'
      const action = 'deactivate'
      const comments = 'Deactivating user'

      const errorMessage = 'API Error'
      secureapi.put = vi.fn().mockRejectedValue(new Error(errorMessage))

      await changeUserStatus(userId, action, dispatch, comments)

      expect(dispatch).toHaveBeenCalledWith(setIsLoadingEditUser(true))
      expect(secureapi.put).toHaveBeenCalledWith(
        `/backoffice-auth/users/${action}/${userId}`,
        { comments }
      )
      expect(dispatch).toHaveBeenCalledWith(
        setNotification({
          message: errorMessage,
          type: 'error',
        })
      )
      expect(dispatch).toHaveBeenCalledWith(setIsLoadingEditUser(false))
    })
    it('should dispatch error notification and stop loading when network issue occurs', async () => {
      const dispatch = vi.fn()
      const userId = '123'
      const action = 'activate'
      const comments = 'Activating user'

      secureapi.put = vi.fn().mockRejectedValue(new Error('Network Error'))
      const getUsers = vi.fn()

      await changeUserStatus(userId, action, dispatch, comments)

      expect(dispatch).toHaveBeenCalledWith(setIsLoadingEditUser(true))
      expect(secureapi.put).toHaveBeenCalledWith(
        `/backoffice-auth/users/${action}/${userId}`,
        { comments }
      )
      expect(dispatch).toHaveBeenCalledWith(
        setNotification({
          message: 'Network Error',
          type: 'error',
        })
      )
      expect(dispatch).toHaveBeenCalledWith(setIsLoadingEditUser(false))
      expect(getUsers).not.toHaveBeenCalled()
    })
  })
  describe('getUserADProfile', () => {
    it('should dispatch loading state before making the API call', async () => {
      const dispatch = vi.fn()
      const searchParams = '<EMAIL>'
      secureapi.get = vi.fn().mockResolvedValue({ data: [] })

      await getUserADProfile(searchParams, dispatch)

      expect(dispatch).toHaveBeenCalledWith(setLoadingADUserDetails(true))
      expect(dispatch).toHaveBeenCalledWith(setLoadingADSuccess(false))
      expect(dispatch).toHaveBeenCalledWith(setLoadingADFailure(false))
    })
    it('should not dispatch any actions when searchParams is empty', async () => {
      const dispatch = vi.fn()
      const searchParams = ''

      await getUserADProfile(searchParams, dispatch)

      expect(dispatch).not.toHaveBeenCalled()
    })
    it('should dispatch failure state and notification on API failure', async () => {
      const dispatch = vi.fn()
      const searchParams = '<EMAIL>'
      const mockData = { data: 'mocked data' }
      const mockError = new Error('API failed')
      secureapi.get = vi.fn().mockRejectedValue(mockError)
      const setNotification = vi.fn()

      await getUserADProfile(searchParams, dispatch)
      setNotification({
        message: 'An error occurred, Please try again',
        type: 'error',
      })

      expect(dispatch).toHaveBeenCalledWith(setLoadingADUserDetails(true))
      expect(dispatch).toHaveBeenCalledWith(setLoadingADSuccess(false))
      expect(dispatch).toHaveBeenCalledWith(setLoadingADFailure(false))
      expect(secureapi.get).toHaveBeenCalledWith(
        '/backoffice-auth/users/search/adusers?email=<EMAIL>'
      )
      expect(dispatch).toHaveBeenCalledWith(setLoadingADFailure(true))
      expect(dispatch).toHaveBeenCalledWith(setLoadingADUserDetails(false))
      expect(setNotification).toHaveBeenCalledWith({
        message: 'An error occurred, Please try again',
        type: 'error',
      })
    })
  })
  describe('assignUserLoanProducts', () => {
    it('should dispatch success notification when loan products are assigned successfully', async () => {
      const mockDispatch = vi.fn()
      const mockData = ['loan1', 'loan2']
      const mockUserId = 'user123'
      secureapi.put = vi.fn().mockResolvedValue({})

      await assignUserLoanProducts(mockData, mockDispatch, mockUserId)

      expect(mockDispatch).toHaveBeenCalledWith(setIsLoadingEditUser(true))
      expect(secureapi.put).toHaveBeenCalledWith(
        `/backoffice-auth/users/${mockUserId}/resources`,
        {
          resources: [
            {
              resourceType: 'PRODUCTS',
              resourceIds: mockData,
            },
          ],
        }
      )
      expect(mockDispatch).toHaveBeenCalledWith(
        setNotification({
          message: 'User loan products assigned successfully',
          type: 'success',
        })
      )
      expect(mockDispatch).toHaveBeenCalledWith(setIsLoadingEditUser(false))
    })

    it('should dispatch success notification when loan products are assigned successfully', async () => {
      const mockDispatch = vi.fn()
      const mockData = ['loan1', 'loan2']
      const mockUserId = 'user123'
      secureapi.put = vi.fn().mockResolvedValue({})

      await assignUserLoanProducts(mockData, mockDispatch, mockUserId)

      expect(mockDispatch).toHaveBeenCalledWith(setIsLoadingEditUser(true))
      expect(secureapi.put).toHaveBeenCalledWith(
        `/backoffice-auth/users/${mockUserId}/resources`,
        {
          resources: [
            {
              resourceType: 'PRODUCTS',
              resourceIds: mockData,
            },
          ],
        }
      )
      expect(mockDispatch).toHaveBeenCalledWith(
        setNotification({
          message: 'User loan products assigned successfully',
          type: 'success',
        })
      )
      expect(mockDispatch).toHaveBeenCalledWith(setIsLoadingEditUser(false))
    })

    it('should reset loading state after API call, regardless of success or failure', async () => {
      const mockDispatch = vi.fn()
      const mockData = ['loan1', 'loan2']
      const mockUserId = 'user123'
      secureapi.put = vi.fn().mockResolvedValue({})

      await assignUserLoanProducts(mockData, mockDispatch, mockUserId)

      expect(mockDispatch).toHaveBeenCalledWith(setIsLoadingEditUser(true))
      expect(secureapi.put).toHaveBeenCalledWith(
        `/backoffice-auth/users/${mockUserId}/resources`,
        {
          resources: [
            {
              resourceType: 'PRODUCTS',
              resourceIds: mockData,
            },
          ],
        }
      )
      expect(mockDispatch).toHaveBeenCalledWith(
        setNotification({
          message: 'User loan products assigned successfully',
          type: 'success',
        })
      )
      expect(mockDispatch).toHaveBeenCalledWith(setIsLoadingEditUser(false))
    })

    it('should dispatch error notification when API call fails', async () => {
      const mockDispatch = vi.fn()
      const mockData = ['loan1', 'loan2']
      const mockUserId = 'user123'
      const errorMessage = 'API Error'
      secureapi.put = vi.fn().mockRejectedValue(new Error(errorMessage))

      await assignUserLoanProducts(mockData, mockDispatch, mockUserId)

      expect(mockDispatch).toHaveBeenCalledWith(setIsLoadingEditUser(true))
      expect(secureapi.put).toHaveBeenCalledWith(
        `/backoffice-auth/users/${mockUserId}/resources`,
        {
          resources: [
            {
              resourceType: 'PRODUCTS',
              resourceIds: mockData,
            },
          ],
        }
      )
      expect(mockDispatch).toHaveBeenCalledWith(
        setNotification({
          message: errorMessage,
          type: 'error',
        })
      )
      expect(mockDispatch).toHaveBeenCalledWith(setIsLoadingEditUser(false))
    })
  })

  describe('generateUserReports', () => {
    type FileFormat = 'excel' | 'csv' | 'json' | 'pdf'
    it('should generate a user report in the specified format when valid parameters are provided', async () => {
      const dispatch = vi.fn()
      const params = {
        firstName: 'John',
        lastName: 'Doe',
        email: '<EMAIL>',
        status: 'active',
        phoneNumber: '1234567890',
        size: 10,
        page: 1,
        roleIds: ['admin'],
        dateCreated: '2023-01-01',
        loginStartdate: '2023-01-01',
        loginEndDate: '2023-12-31',
      }
      const format = 'csv'
      const filteredData: string[] = []

      vi.spyOn(secureapi2, 'get').mockResolvedValue({
        data: new ArrayBuffer(8),
      })
      //@ts-ignore
      vi.spyOn(global, 'Blob').mockImplementationOnce((data, options) => ({
        data,
        options,
      }))
      //@ts-ignore
      vi.spyOn(global, 'URL').mockImplementation(() => ({
        createObjectURL: vi.fn(),
      }))
      //@ts-ignore

      await generateUserReports({ dispatch, params, format, filteredData })

      expect(dispatch).toHaveBeenCalledWith(setGeneratedUserReportLoading(true))
      expect(dispatch).toHaveBeenCalledWith(setGeneratedUserReportSuccess(true))
      expect(secureapi2.get).toHaveBeenCalledWith(
        expect.stringContaining('/reports/users/export-to-csv'),
        { responseType: 'arraybuffer' }
      )
    })
    it('should handle unsupported file format by dispatching failure and notification', async () => {
      const dispatch = vi.fn()
      const params = {
        dateCreated: '2023-01-01',
        loginStartdate: '2023-01-01',
        loginEndDate: '2023-12-31',
      }
      const format = 'unsupportedFormat' as FileFormat
      const filteredData: string[] = []

      // @ts-ignore
      await generateUserReports({ dispatch, params, format, filteredData })

      expect(dispatch).toHaveBeenCalledWith(setGeneratedUserReportFailure(true))
      expect(dispatch).toHaveBeenCalledWith(
        setNotification(
          expect.objectContaining({
            type: 'error',
          })
        )
      )
      expect(dispatch).toHaveBeenCalledWith(
        setGeneratedUserReportLoading(false)
      )
    })
  })
})
