import { render, screen } from '@testing-library/react'
import { describe, it, expect } from 'vitest'
import AppProvider from '@/store/AppProvider'

describe('AppProvider', () => {
  it('renders children within Redux Provider', () => {
    const TestComponent = () => <div data-testid="test-child">Test Child</div>

    render(
      <AppProvider>
        <TestComponent />
      </AppProvider>
    )

    expect(screen.getByTestId('test-child')).toBeInTheDocument()
    expect(screen.getByText('Test Child')).toBeInTheDocument()
  })

  it('provides Redux store to children', () => {
    const TestComponent = () => {
      // This component would normally use useSelector or useDispatch
      // For this test, we just verify it renders without errors
      return <div data-testid="redux-consumer">Redux Consumer</div>
    }

    render(
      <AppProvider>
        <TestComponent />
      </AppProvider>
    )

    expect(screen.getByTestId('redux-consumer')).toBeInTheDocument()
  })

  it('handles multiple children', () => {
    render(
      <AppProvider>
        <div data-testid="child-1">Child 1</div>
        <div data-testid="child-2">Child 2</div>
      </AppProvider>
    )

    expect(screen.getByTestId('child-1')).toBeInTheDocument()
    expect(screen.getByTestId('child-2')).toBeInTheDocument()
  })
})
