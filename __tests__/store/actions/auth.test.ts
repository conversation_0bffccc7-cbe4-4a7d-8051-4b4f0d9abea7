import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest'
import { handleLogout } from '@/store/actions/auth'
import { secureapi } from '@dtbx/store/utils'
import {
  clearCustomerState,
  resetRolesStore,
  resetUsersStore,
} from '@/store/reducers'
import { clearNotification } from '@dtbx/store/reducers'

// Mock the dependencies
vi.mock('@dtbx/store/utils', () => ({
  secureapi: {
    post: vi.fn(),
  },
}))

vi.mock('@/store/reducers', () => ({
  clearCustomerState: vi.fn(),
  resetRolesStore: vi.fn(),
  resetUsersStore: vi.fn(),
}))

vi.mock('@dtbx/store/reducers', () => ({
  clearNotification: vi.fn(),
}))

describe('auth actions', () => {
  const mockDispatch = vi.fn()
  const mockRouter = {
    push: vi.fn(),
    replace: vi.fn(),
    back: vi.fn(),
    forward: vi.fn(),
    refresh: vi.fn(),
    prefetch: vi.fn(),
  }

  // Mock localStorage
  const mockLocalStorage = {
    clear: vi.fn(),
    getItem: vi.fn(),
    setItem: vi.fn(),
    removeItem: vi.fn(),
  }

  beforeEach(() => {
    vi.clearAllMocks()
    Object.defineProperty(window, 'localStorage', {
      value: mockLocalStorage,
      writable: true,
    })
    vi.spyOn(console, 'error').mockImplementation(() => {})
  })

  afterEach(() => {
    vi.restoreAllMocks()
  })

  describe('handleLogout', () => {
    it('should successfully logout and clear all state', async () => {
      // Mock successful API call
      secureapi.post = vi.fn().mockResolvedValue({})

      await handleLogout(mockDispatch, mockRouter as any)

      // Verify API call
      expect(secureapi.post).toHaveBeenCalledWith('/backoffice-auth/users/logout')

      // Verify all dispatch calls
      expect(mockDispatch).toHaveBeenCalledWith(resetUsersStore())
      expect(mockDispatch).toHaveBeenCalledWith(resetRolesStore())
      expect(mockDispatch).toHaveBeenCalledWith(clearNotification())
      expect(mockDispatch).toHaveBeenCalledWith(clearCustomerState())

      // Verify localStorage is cleared
      expect(mockLocalStorage.clear).toHaveBeenCalled()

      // Verify navigation
      expect(mockRouter.push).toHaveBeenCalledWith('/')
    })

    it('should handle logout API failure gracefully', async () => {
      const errorMessage = 'Logout failed'
      secureapi.post = vi.fn().mockRejectedValue(new Error(errorMessage))

      await handleLogout(mockDispatch, mockRouter as any)

      // Verify API call was made
      expect(secureapi.post).toHaveBeenCalledWith('/backoffice-auth/users/logout')

      // Verify error is logged
      expect(console.error).toHaveBeenCalledWith('ERROR ON LOGOUT', expect.any(Error))

      // On error, the cleanup doesn't happen in the actual implementation
      expect(mockDispatch).not.toHaveBeenCalled()
      expect(mockLocalStorage.clear).not.toHaveBeenCalled()
      expect(mockRouter.push).not.toHaveBeenCalled()
    })

    it('should handle network errors during logout', async () => {
      secureapi.post = vi.fn().mockRejectedValue(new Error('Network Error'))

      await handleLogout(mockDispatch, mockRouter as any)

      expect(console.error).toHaveBeenCalledWith('ERROR ON LOGOUT', expect.any(Error))

      // Verify cleanup doesn't happen on error
      expect(mockDispatch).not.toHaveBeenCalled()
      expect(mockLocalStorage.clear).not.toHaveBeenCalled()
      expect(mockRouter.push).not.toHaveBeenCalled()
    })

    it('should call all actions in the correct order', async () => {
      secureapi.post = vi.fn().mockResolvedValue({})

      await handleLogout(mockDispatch, mockRouter as any)

      // Verify the order of dispatch calls
      const dispatchCalls = mockDispatch.mock.calls
      expect(dispatchCalls[0][0]).toEqual(resetUsersStore())
      expect(dispatchCalls[1][0]).toEqual(resetRolesStore())
      expect(dispatchCalls[2][0]).toEqual(clearNotification())
      expect(dispatchCalls[3][0]).toEqual(clearCustomerState())
    })
  })
})
