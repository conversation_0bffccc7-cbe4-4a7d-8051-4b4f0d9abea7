import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest'
import {
  getUsers,
  getUsersByRoleId,
  getUserById,
  createUser,
  makeCreateUser,
  approveCreateUser,
  rejectCreateUser,
  updateUser,
  makeUpdateUser,
  approveUpdateUser,
  rejectUpdateUser,
  activateUser,
  deactivateUser,
  changeUserStatus,
  getUserADProfile,
  assignUserLoanProducts,
  assignUserPlatforms,
  generateUserReports,
  getLoanProducts,
  getuserPlatform,
} from '@/store/actions/staffUsers'
import {
  setIsLoadingUsers,
  setUsersResponse,
  setRoleUsers,
  setSingleUserData,
  setIsLoadingCreateUser,
  setIsLoadingEditUser,
  setLoadingADUserDetails,
  setLoadingADSuccess,
  setLoadingADFailure,
  setLoadedADUserDetails,
  setGeneratedUserReportLoading,
  setGeneratedUserReportSuccess,
  setGeneratedUserReportFailure,
  setIsLoadingLoanProducts,
  setLoanProducts,
  setLoanProductsSummary,
  setUserProducts,
  setIsLoadingPlatforms,
  setPlatforms,
  setPlatformsSummary,
} from '@/store/reducers'
import { setNotification } from '@dtbx/store/reducers'
import { secureapi, secureapi2, downloadBlob } from '@dtbx/store/utils'
import { getApprovals } from '@/store/actions/approvalRequests'
import { refreshToken } from '@dtbx/store/actions'
import { jwtDecode } from 'jwt-decode'

// Mock the dependencies
vi.mock('@dtbx/store/utils', () => ({
  secureapi: {
    get: vi.fn(),
    post: vi.fn(),
    put: vi.fn(),
  },
  secureapi2: {
    get: vi.fn(),
  },
  downloadBlob: vi.fn(),
}))

vi.mock('@/store/reducers', () => ({
  setIsLoadingUsers: vi.fn(),
  setUsersResponse: vi.fn(),
  setRoleUsers: vi.fn(),
  setSingleUserData: vi.fn(),
  setIsLoadingCreateUser: vi.fn(),
  setIsLoadingEditUser: vi.fn(),
  setLoadingADUserDetails: vi.fn(),
  setLoadingADSuccess: vi.fn(),
  setLoadingADFailure: vi.fn(),
  setLoadedADUserDetails: vi.fn(),
  setGeneratedUserReportLoading: vi.fn(),
  setGeneratedUserReportSuccess: vi.fn(),
  setGeneratedUserReportFailure: vi.fn(),
  setIsLoadingLoanProducts: vi.fn(),
  setLoanProducts: vi.fn(),
  setLoanProductsSummary: vi.fn(),
  setUserProducts: vi.fn(),
  setIsLoadingPlatforms: vi.fn(),
  setPlatforms: vi.fn(),
  setPlatformsSummary: vi.fn(),
}))

vi.mock('@dtbx/store/reducers', () => ({
  setNotification: vi.fn(),
}))

vi.mock('@/store/actions/approvalRequests', () => ({
  getApprovals: vi.fn(),
}))

vi.mock('@dtbx/store/actions', () => ({
  refreshToken: vi.fn(),
}))

vi.mock('jwt-decode', () => ({
  jwtDecode: vi.fn(),
}))

// Mock localStorage
const mockLocalStorage = {
  getItem: vi.fn(),
  setItem: vi.fn(),
  removeItem: vi.fn(),
  clear: vi.fn(),
}

Object.defineProperty(window, 'localStorage', {
  value: mockLocalStorage,
  writable: true,
})

describe('staffUsers actions', () => {
  const mockDispatch = vi.fn()

  beforeEach(() => {
    vi.clearAllMocks()
    vi.spyOn(console, 'error').mockImplementation(() => {})
  })

  afterEach(() => {
    vi.restoreAllMocks()
  })

  describe('getUsers', () => {
    it('should successfully fetch users with parameters', async () => {
      const mockParams = {
        firstName: 'John',
        lastName: 'Doe',
        email: '<EMAIL>',
        status: 'ACTIVE',
        phoneNumber: '1234567890',
        size: 10,
        page: 1,
        roleIds: ['role1', 'role2'],
        dateCreatedFrom: '2023-01-01',
        dateCreatedTo: '2023-12-31',
        lastLoginDateFrom: '2023-01-01',
        lastLoginDateTo: '2023-12-31',
      }

      const mockResponse = {
        data: {
          data: [
            { id: '1', firstName: 'John', lastName: 'Doe' },
            { id: '2', firstName: 'Jane', lastName: 'Smith' },
          ],
        },
      }

      secureapi.get = vi.fn().mockResolvedValue(mockResponse)

      await getUsers(mockDispatch, mockParams)

      expect(mockDispatch).toHaveBeenCalledWith(setIsLoadingUsers(true))
      expect(secureapi.get).toHaveBeenCalledWith(
        expect.stringContaining('/backoffice-auth/users?')
      )
      expect(mockDispatch).toHaveBeenCalledWith(setUsersResponse(mockResponse.data))
      expect(mockDispatch).toHaveBeenCalledWith(setIsLoadingUsers(false))
    })

    it('should handle API failure', async () => {
      const errorMessage = 'Failed to fetch users'
      secureapi.get = vi.fn().mockRejectedValue(new Error(errorMessage))

      await getUsers(mockDispatch, { page: 1, size: 10 })

      expect(mockDispatch).toHaveBeenCalledWith(setIsLoadingUsers(true))
      expect(mockDispatch).toHaveBeenCalledWith(setIsLoadingUsers(false))
      expect(mockDispatch).toHaveBeenCalledWith(
        setNotification({
          message: errorMessage,
          type: 'error',
        })
      )
    })

    it('should not make API call when no params provided', async () => {
      await getUsers(mockDispatch)

      expect(secureapi.get).not.toHaveBeenCalled()
      expect(mockDispatch).not.toHaveBeenCalled()
    })
  })

  describe('getUsersByRoleId', () => {
    it('should successfully fetch users by role ID', async () => {
      const roleId = 'role-123'
      const page = 1
      const size = 10
      const params = '&status=ACTIVE'

      const mockResponse = {
        data: {
          data: [{ id: '1', firstName: 'John', lastName: 'Doe' }],
        },
      }

      secureapi.get = vi.fn().mockResolvedValue(mockResponse)

      await getUsersByRoleId(mockDispatch, roleId, page, size, params)

      expect(mockDispatch).toHaveBeenCalledWith(setIsLoadingUsers(true))
      expect(secureapi.get).toHaveBeenCalledWith(
        `/backoffice-auth/roles/${roleId}/users?page=${page}&size=${size}${params}`
      )
      expect(mockDispatch).toHaveBeenCalledWith(setRoleUsers(mockResponse.data))
      expect(mockDispatch).toHaveBeenCalledWith(setIsLoadingUsers(false))
    })

    it('should handle API failure', async () => {
      const errorMessage = 'Failed to fetch role users'
      secureapi.get = vi.fn().mockRejectedValue(new Error(errorMessage))

      await getUsersByRoleId(mockDispatch, 'role-123', 1, 10)

      expect(mockDispatch).toHaveBeenCalledWith(setIsLoadingUsers(true))
      expect(mockDispatch).toHaveBeenCalledWith(setIsLoadingUsers(false))
      expect(mockDispatch).toHaveBeenCalledWith(
        setNotification({
          message: errorMessage,
          type: 'error',
        })
      )
    })
  })

  describe('getUserById', () => {
    it('should successfully fetch user by ID', async () => {
      const userId = 'user-123'
      const mockUser = { id: userId, firstName: 'John', lastName: 'Doe' }
      const mockResponse = { data: mockUser }

      secureapi.get = vi.fn().mockResolvedValue(mockResponse)

      await getUserById(mockDispatch, userId)

      expect(secureapi.get).toHaveBeenCalledWith(`/backoffice-auth/users/${userId}`)
      expect(mockDispatch).toHaveBeenCalledWith(setSingleUserData(mockUser))
    })

    it('should handle API failure', async () => {
      const userId = 'user-123'
      secureapi.get = vi.fn().mockRejectedValue(new Error('User not found'))

      await getUserById(mockDispatch, userId)

      expect(console.error).toHaveBeenCalledWith('Error fetching user by id', expect.any(Error))
    })
  })

  describe('createUser', () => {
    const mockUserData = {
      firstName: 'John',
      lastName: 'Doe',
      email: '<EMAIL>',
      phoneNumber: '1234567890',
      roleIds: ['role1'],
    }

    it('should successfully create user', async () => {
      secureapi.post = vi.fn().mockResolvedValue({})

      await createUser(mockUserData, mockDispatch)

      expect(mockDispatch).toHaveBeenCalledWith(setIsLoadingCreateUser(true))
      expect(secureapi.post).toHaveBeenCalledWith('/backoffice-auth/users', mockUserData)
      expect(mockDispatch).toHaveBeenCalledWith(setIsLoadingCreateUser(false))
      expect(mockDispatch).toHaveBeenCalledWith(
        setNotification({
          message: 'User was Successfully Created',
          type: 'success',
        })
      )
    })

    it('should handle API failure', async () => {
      const errorMessage = 'Creation failed'
      secureapi.post = vi.fn().mockRejectedValue(new Error(errorMessage))

      await createUser(mockUserData, mockDispatch)

      expect(mockDispatch).toHaveBeenCalledWith(setIsLoadingCreateUser(true))
      expect(mockDispatch).toHaveBeenCalledWith(setIsLoadingCreateUser(false))
      expect(mockDispatch).toHaveBeenCalledWith(
        setNotification({
          message: errorMessage,
          type: 'error',
        })
      )
    })
  })

  describe('getLoanProducts', () => {
    it('should successfully fetch loan products', async () => {
      const mockProducts = [
        { id: 'product1', name: 'Personal Loan' },
        { id: 'product2', name: 'Business Loan' },
      ]
      const mockResponse = {
        data: {
          data: mockProducts,
          totalElements: 2,
          totalPages: 1,
          pageNumber: 0,
          pageSize: 10,
        },
      }

      const mockToken = 'mock-jwt-token'
      const mockDecodedToken = {
        resources: [
          {
            resourceIds: ['product1'],
          },
        ],
      }

      mockLocalStorage.getItem.mockReturnValue(mockToken)
      jwtDecode.mockReturnValue(mockDecodedToken)
      secureapi2.get = vi.fn().mockResolvedValue(mockResponse)

      const result = await getLoanProducts(mockDispatch, 'status=ACTIVE')

      expect(mockDispatch).toHaveBeenCalledWith(setIsLoadingLoanProducts(true))
      expect(secureapi2.get).toHaveBeenCalledWith('/lms/products?status=ACTIVE')
      expect(mockDispatch).toHaveBeenCalledWith(setLoanProducts(mockProducts))
      expect(mockDispatch).toHaveBeenCalledWith(
        setLoanProductsSummary({
          totalElements: 2,
          totalPages: 1,
          pageNumber: 0,
          pageSize: 10,
        })
      )
      expect(mockDispatch).toHaveBeenCalledWith(
        setUserProducts([{ id: 'product1', name: 'Personal Loan' }])
      )
      expect(mockDispatch).toHaveBeenCalledWith(setIsLoadingLoanProducts(false))
      expect(result).toEqual(mockResponse.data)
    })

    it('should handle API failure', async () => {
      secureapi2.get = vi.fn().mockRejectedValue(new Error('Failed to fetch'))

      await getLoanProducts(mockDispatch)

      expect(mockDispatch).toHaveBeenCalledWith(setIsLoadingLoanProducts(true))
      expect(mockDispatch).toHaveBeenCalledWith(setIsLoadingLoanProducts(false))
    })

    it('should handle missing token', async () => {
      const mockProducts = [{ id: 'product1', name: 'Personal Loan' }]
      const mockResponse = {
        data: {
          data: mockProducts,
          totalElements: 1,
          totalPages: 1,
          pageNumber: 0,
          pageSize: 10,
        },
      }

      mockLocalStorage.getItem.mockReturnValue(null)
      secureapi2.get = vi.fn().mockResolvedValue(mockResponse)

      await getLoanProducts(mockDispatch)

      expect(mockDispatch).toHaveBeenCalledWith(setLoanProducts(mockProducts))
      expect(mockDispatch).toHaveBeenCalledWith(setIsLoadingLoanProducts(false))
    })
  })

  describe('getuserPlatform', () => {
    it('should successfully fetch user platforms', async () => {
      const mockPlatforms = [
        { id: 'platform1', name: 'Mobile App' },
        { id: 'platform2', name: 'Web Portal' },
      ]
      const mockResponse = {
        data: {
          data: mockPlatforms,
          totalElements: 2,
          totalPages: 1,
          pageNumber: 0,
          pageSize: 10,
        },
      }

      secureapi2.get = vi.fn().mockResolvedValue(mockResponse)

      await getuserPlatform(mockDispatch, 'status=ACTIVE')

      expect(mockDispatch).toHaveBeenCalledWith(setIsLoadingPlatforms(true))
      expect(secureapi2.get).toHaveBeenCalledWith('/dbp/platforms?status=ACTIVE')
      expect(mockDispatch).toHaveBeenCalledWith(setPlatforms(mockPlatforms))
      expect(mockDispatch).toHaveBeenCalledWith(
        setPlatformsSummary({
          totalElements: 2,
          totalPages: 1,
          pageNumber: 0,
          pageSize: 10,
        })
      )
      expect(mockDispatch).toHaveBeenCalledWith(setIsLoadingPlatforms(false))
    })

    it('should handle API failure', async () => {
      secureapi2.get = vi.fn().mockRejectedValue(new Error('Failed to fetch platforms'))

      await getuserPlatform(mockDispatch)

      expect(mockDispatch).toHaveBeenCalledWith(setIsLoadingPlatforms(true))
      expect(mockDispatch).toHaveBeenCalledWith(setIsLoadingPlatforms(false))
      expect(console.error).toHaveBeenCalledWith('Error fetching platforms: ', expect.any(Error))
    })
  })

  describe('assignUserLoanProducts', () => {
    it('should successfully assign loan products to user', async () => {
      const productIds = ['product1', 'product2']
      const userId = 'user-123'

      secureapi.put = vi.fn().mockResolvedValue({})

      await assignUserLoanProducts(productIds, mockDispatch, userId)

      expect(mockDispatch).toHaveBeenCalledWith(setIsLoadingEditUser(true))
      expect(secureapi.put).toHaveBeenCalledWith(`/backoffice-auth/users/${userId}/resources`, {
        resources: [
          {
            resourceType: 'PRODUCTS',
            resourceIds: productIds,
          },
        ],
      })
      expect(mockDispatch).toHaveBeenCalledWith(
        setNotification({
          message: 'User loan products assigned successfully',
          type: 'success',
        })
      )
      expect(mockDispatch).toHaveBeenCalledWith(setIsLoadingEditUser(false))
    })

    it('should handle API failure', async () => {
      const errorMessage = 'Assignment failed'
      secureapi.put = vi.fn().mockRejectedValue(new Error(errorMessage))

      await assignUserLoanProducts(['product1'], mockDispatch, 'user-123')

      expect(mockDispatch).toHaveBeenCalledWith(setIsLoadingEditUser(true))
      expect(mockDispatch).toHaveBeenCalledWith(setIsLoadingEditUser(false))
      expect(mockDispatch).toHaveBeenCalledWith(
        setNotification({
          message: errorMessage,
          type: 'error',
        })
      )
    })
  })

  describe('assignUserPlatforms', () => {
    it('should successfully assign platforms to user', async () => {
      const platformIds = ['platform1', 'platform2']
      const userId = 'user-123'

      secureapi.put = vi.fn().mockResolvedValue({})

      await assignUserPlatforms(platformIds, mockDispatch, userId)

      expect(mockDispatch).toHaveBeenCalledWith(setIsLoadingEditUser(true))
      expect(secureapi.put).toHaveBeenCalledWith(`/backoffice-auth/users/${userId}/resources`, {
        resources: [
          {
            resourceType: 'PLATFORMS',
            resourceIds: platformIds,
          },
        ],
      })
      expect(mockDispatch).toHaveBeenCalledWith(
        setNotification({
          message: 'User platforms assigned successfully',
          type: 'success',
        })
      )
      expect(mockDispatch).toHaveBeenCalledWith(setIsLoadingEditUser(false))
    })

    it('should handle API failure', async () => {
      const errorMessage = 'Platform assignment failed'
      secureapi.put = vi.fn().mockRejectedValue(new Error(errorMessage))

      await assignUserPlatforms(['platform1'], mockDispatch, 'user-123')

      expect(mockDispatch).toHaveBeenCalledWith(setIsLoadingEditUser(true))
      expect(mockDispatch).toHaveBeenCalledWith(setIsLoadingEditUser(false))
      expect(mockDispatch).toHaveBeenCalledWith(
        setNotification({
          message: errorMessage,
          type: 'error',
        })
      )
    })
  })
})
