import { IUser, IUsersResponse, ICreateUser, IUpdateUser } from '@/store/interfaces'

export const userStub: IUser = {
  id: '123',
  firstName: '<PERSON>',
  lastName: 'Do<PERSON>',
  middleName: '<PERSON>',
  roles: [
    {
      id: 'role1',
      name: 'Admin',
      description: 'Administrator role',
      creationDate: '2023-01-01',
      custom: false,
      permissions: [],
      permissionsGroup: [],
    },
  ],
  email: '<EMAIL>',
  phoneNumber: '+1234567890',
  dateCreated: '2023-01-01T10:00:00Z',
  status: 'ACTIVE',
  country: 'US',
  lastLoginDate: '2023-12-01T10:00:00Z',
}

export const usersResponseStub: IUsersResponse = {
  pageNumber: 1,
  pageSize: 10,
  totalNumberOfPages: 1,
  totalElements: 1,
  data: [userStub],
}

export const createUserStub: ICreateUser = {
  firstName: 'Jane',
  lastName: '<PERSON>',
  middleName: '<PERSON>',
  roleIds: ['role1'],
  email: '<EMAIL>',
  phoneNumber: '+254787654321',
  comments: 'Creating new user for testing',
}

export const updateUserStub: IUpdateUser = {
  roleIds: ['role1', 'role2'],
  comments: 'Updating user roles for testing',
}
