import { NextAppDirEmotionCacheProvider, ThemeConfig } from '@dtbx/ui/theme'
import React, { ReactNode } from 'react'
import { render, RenderOptions } from '@testing-library/react'
import { AppStore, RootState, setupStore } from '@/store'
import { Provider } from 'react-redux'
import { vi } from 'vitest'

// Create router mocks
const baseRouterMock = {
  push: vi.fn(),
  replace: vi.fn(),
  prefetch: vi.fn(),
  back: vi.fn(),
  forward: vi.fn(),
  refresh: vi.fn(),
  pathname: '/roles',
}

const customRouterMock = {
  ...baseRouterMock,
  pushWithTrailingSlash: vi.fn((url) => {
    const urlWithTrailingSlash = url.endsWith('/') ? url : `${url}/`
    baseRouterMock.push(urlWithTrailingSlash)
  }),
}

// Mock Next.js navigation
vi.mock('next/navigation', () => ({
  useRouter: () => baseRouterMock,
  usePathname: () => '/roles',
  useSearchParams: () => new URLSearchParams(),
  useParams: () => ({}),
  useServerInsertedHTML: vi.fn((callback) => callback()),
}))

// Mock the CustomRouter hook
vi.mock('@dtbx/ui/hooks', () => ({
  useCustomRouter: () => customRouterMock,
}))

// Mock store
const mockStore = {
  getState: vi.fn(() => ({
    roles: {
      roles: [],
      isLoading: false,
      totalElements: 0,
      totalNumberOfPages: 0,
      pageNumber: 1,
      pageSize: 10,
    },
    auth: {
      decodedToken: {
        permissions: [],
      },
    },
    navigation: {
      isSidebarCollapsed: false,
    },
    notifications: {
      localNotification: null,
      localNotificationType: null,
    },
  })),
  dispatch: vi.fn(),
  subscribe: vi.fn(),
}

vi.mock('@/store', () => ({
  useAppDispatch: vi.fn(() => vi.fn()),
  useAppSelector: vi.fn((selector) => selector(mockStore.getState())),
  setupStore: vi.fn(() => mockStore),
}))

interface ExtendedRenderOptions
  extends Omit<RenderOptions, 'queries' | 'wrapper'> {
  preloadedState?: Partial<RootState>
  store?: AppStore
}

const renderWithProviders = (
  ui: ReactNode,
  {
    preloadedState = {},
    store = setupStore(preloadedState),
    ...renderOptions
  }: ExtendedRenderOptions = {}
) => {
  const Wrapper = ({ children }: { children: ReactNode }) => {
    return (
      <Provider store={store}>
        <NextAppDirEmotionCacheProvider options={{ key: 'mui' }}>
          <ThemeConfig themeType={'main'}>{children}</ThemeConfig>
        </NextAppDirEmotionCacheProvider>
      </Provider>
    )
  }
  return render(ui, { wrapper: Wrapper, ...renderOptions })
}

export * from '@testing-library/react'
export { renderWithProviders as render, customRouterMock, baseRouterMock }