import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest'
import {
  getTimeOfDay,
  getWeekOfYear,
  getAuctionWeek,
  formatCustomDate,
  formatDateTime,
  isObjEmpty,
  trimSpace,
  generateMarks,
  getBase64,
  isAccountLinkingApprovalRequest,
  formatText,
  addUnderscores,
  formatCamelCaseToWords,
} from '@dtbx/store/utils'

describe('Helper Functions', () => {
  describe('getTimeOfDay', () => {
    beforeEach(() => {
      vi.useFakeTimers()
    })

    afterEach(() => {
      vi.useRealTimers()
    })

    it('returns morning for hours before 12', () => {
      vi.setSystemTime(new Date('2023-01-01 10:30:00'))
      const result = getTimeOfDay()
      expect(result.timeOfDay).toBe('morning')
      expect(result.formattedTime).toBe('10:30 AM')
    })

    it('returns afternoon for hours 12-17', () => {
      vi.setSystemTime(new Date('2023-01-01 15:45:00'))
      const result = getTimeOfDay()
      expect(result.timeOfDay).toBe('afternoon')
      expect(result.formattedTime).toBe('3:45 PM')
    })

    it('returns evening for hours 18 and later', () => {
      vi.setSystemTime(new Date('2023-01-01 20:15:00'))
      const result = getTimeOfDay()
      expect(result.timeOfDay).toBe('evening')
      expect(result.formattedTime).toBe('8:15 PM')
    })

    it('handles midnight correctly', () => {
      vi.setSystemTime(new Date('2023-01-01 00:00:00'))
      const result = getTimeOfDay()
      expect(result.timeOfDay).toBe('morning')
      expect(result.formattedTime).toBe('12:00 AM')
    })

    it('handles noon correctly', () => {
      vi.setSystemTime(new Date('2023-01-01 12:00:00'))
      const result = getTimeOfDay()
      expect(result.timeOfDay).toBe('afternoon')
      expect(result.formattedTime).toBe('12:00 PM')
    })
  })

  describe('getWeekOfYear', () => {
    it('calculates week number correctly for start of year', () => {
      const date = new Date('2023-01-01')
      const week = getWeekOfYear(date)
      // January 1, 2023 was a Sunday, so it could be week 1 or 2 depending on the algorithm
      expect(week).toBeGreaterThanOrEqual(1)
      expect(week).toBeLessThanOrEqual(2)
    })

    it('calculates week number correctly for middle of year', () => {
      const date = new Date('2023-07-01')
      const week = getWeekOfYear(date)
      expect(week).toBeGreaterThan(20)
      expect(week).toBeLessThan(30)
    })

    it('uses current date when no date provided', () => {
      const week = getWeekOfYear()
      expect(week).toBeGreaterThan(0)
      expect(week).toBeLessThanOrEqual(53)
    })
  })

  describe('getAuctionWeek', () => {
    it('returns week number minus 1', () => {
      const currentWeek = getWeekOfYear()
      const auctionWeek = getAuctionWeek()
      expect(auctionWeek).toBe(currentWeek - 1)
    })
  })

  describe('formatCustomDate', () => {
    it('formats date with default formats', () => {
      const result = formatCustomDate('2023-01-15')
      expect(result).toBe('January 15, 2023')
    })

    it('formats date with custom input format', () => {
      const result = formatCustomDate('15/01/2023', 'DD/MM/YYYY')
      expect(result).toBe('January 15, 2023')
    })

    it('formats date with custom output format', () => {
      const result = formatCustomDate('2023-01-15', 'YYYY-MM-DD', 'DD-MM-YYYY')
      expect(result).toBe('15-01-2023')
    })
  })

  describe('formatDateTime', () => {
    it('formats datetime string to locale time', () => {
      const result = formatDateTime('2023-01-15T14:30:00Z')
      expect(result).toMatch(/\d{1,2}:\d{2}:\d{2}\s?(AM|PM)/i)
    })
  })

  describe('isObjEmpty', () => {
    it('returns true for empty object', () => {
      expect(isObjEmpty({})).toBe(true)
    })

    it('returns false for non-empty object', () => {
      expect(isObjEmpty({ key: 'value' })).toBe(false)
    })
  })

  describe('trimSpace', () => {
    it('trims string values in object', () => {
      const input = { name: '  John  ', age: 25 }
      const result = trimSpace(input)
      expect(result.name).toBe('John')
      expect(result.age).toBe(25)
    })

    it('handles nested objects', () => {
      const input = {
        user: { name: '  Jane  ', email: '  <EMAIL>  ' },
        count: 10,
      }
      const result = trimSpace(input)
      expect(result.user.name).toBe('Jane')
      expect(result.user.email).toBe('<EMAIL>')
      expect(result.count).toBe(10)
    })

    it('handles arrays', () => {
      const input = {
        users: [{ name: '  John  ' }, { name: '  Jane  ' }],
      }
      const result = trimSpace(input)
      expect(result.users[0].name).toBe('John')
      expect(result.users[1].name).toBe('Jane')
    })
  })

  describe('generateMarks', () => {
    it('generates marks with correct step', () => {
      const marks = generateMarks(10, 0, 50)
      expect(marks).toHaveLength(6)
      expect(marks[0]).toEqual({ value: 0, label: '0' })
      expect(marks[5]).toEqual({ value: 50, label: '50' })
    })

    it('generates single mark when start equals end', () => {
      const marks = generateMarks(1, 5, 5)
      expect(marks).toHaveLength(1)
      expect(marks[0]).toEqual({ value: 5, label: '5' })
    })
  })

  describe('getBase64', () => {
    it('converts file to base64', async () => {
      const mockFile = new File(['test content'], 'test.txt', {
        type: 'text/plain',
      })
      const result = await getBase64(mockFile)
      expect(typeof result).toBe('string')
      expect(result).toContain('data:text/plain;base64,')
    })

    it('rejects on file reader error', async () => {
      const mockFile = new File([''], 'test.txt')

      // Mock FileReader to simulate error
      const originalFileReader = global.FileReader
      global.FileReader = vi.fn(() => ({
        readAsDataURL: vi.fn(function () {
          setTimeout(() => this.onerror(new Error('Read error')), 0)
        }),
        result: null,
        error: null,
        onload: null,
        onerror: null,
      })) as any

      await expect(getBase64(mockFile)).rejects.toThrow()

      global.FileReader = originalFileReader
    })
  })

  describe('isAccountLinkingApprovalRequest', () => {
    it('returns true for account linking entity', () => {
      const entity = JSON.stringify({ accounts: [] })
      expect(isAccountLinkingApprovalRequest(entity)).toBe(true)
    })

    it('returns false for non-account linking entity', () => {
      const entity = JSON.stringify({ users: [] })
      expect(isAccountLinkingApprovalRequest(entity)).toBe(false)
    })
  })



  describe('formatText', () => {
    it('formats underscore text to uppercase with spaces', () => {
      expect(formatText('user_name')).toBe('USER NAME')
    })

    it('formats camelCase to uppercase with spaces', () => {
      expect(formatText('userName')).toBe('USER NAME')
    })

    it('handles mixed case and underscores', () => {
      expect(formatText('user_firstName')).toBe('USER FIRST NAME')
    })
  })

  describe('addUnderscores', () => {
    it('replaces spaces with underscores', () => {
      expect(addUnderscores('user name')).toBe('user_name')
    })

    it('handles multiple spaces', () => {
      expect(addUnderscores('user   name   here')).toBe('user_name_here')
    })

    it('trims leading and trailing spaces', () => {
      expect(addUnderscores('  user name  ')).toBe('user_name')
    })
  })

  describe('formatCamelCaseToWords', () => {
    it('formats camelCase to words with proper capitalization', () => {
      expect(formatCamelCaseToWords('userName')).toBe('User Name')
    })

    it('handles single word', () => {
      expect(formatCamelCaseToWords('user')).toBe('User')
    })

    it('handles multiple camelCase words', () => {
      expect(formatCamelCaseToWords('userFirstName')).toBe('User First Name')
    })
  })
})
