trigger:
  branches:
    include:
      - dev
      - uat
      - prod

variables:
  vmImageName: 'ubuntu-latest'
  dockerfilePath: 'Dockerfile'
  tag: '$(Build.BuildId)'

resources:
  repositories:
  - repository: templates
    type: git
    name: DevSecOps/azure-pipelines-configs

stages:
  - stage: DEV_CI
    displayName: Build DEV
    condition: and(always(), contains(variables['Build.SourceBranch'], 'refs/heads/dev'))
    jobs:
      - template: microfrontend-dockerapps-build-template.yml@templates
        parameters:
          environment: 'dev'
          vmImageName: $(vmImageName)
          dockerRegistryServiceConnection: 'dtbxmacr-dev'
          images: 
            - repository: 'ams_frontend'
              dockerfilePath: $(dockerfilePath)
              buildContext: '.'
          containerRegistry: 'dtbxdevmcacr.azurecr.io'
          nodeVersion: '20.x'
          tag: $(tag)
          injectNpmrc: true
          permissions:
            pipelines:
              accessToken: 'write'
              
  - stage: UAT_CI
    displayName: Build UAT
    condition: and(always(), contains(variables['Build.SourceBranch'], 'refs/heads/uat'))
    jobs:
      - template: microfrontend-dockerapps-build-template.yml@templates
        parameters:
          environment: 'uat'
          vmImageName: $(vmImageName)
          dockerRegistryServiceConnection: 'dtbxmacr-uat'
          images: 
            - repository: 'ams_frontend'
              dockerfilePath: $(dockerfilePath)
              buildContext: '.'
          containerRegistry: 'dtbxmcacr.azurecr.io'
          nodeVersion: '20.x'
          tag: $(tag)
          injectNpmrc: true
          permissions:
            pipelines:
              accessToken: 'write'

  - stage: PROD_CI
    displayName: Build PROD
    condition: and(always(), contains(variables['Build.SourceBranch'], 'refs/heads/prod'))
    jobs:
      - template: microfrontend-dockerapps-build-template.yml@templates
        parameters:
          environment: 'prod'
          vmImageName: $(vmImageName)
          dockerRegistryServiceConnection: 'dtbxmacr-prod'
          images: 
            - repository: 'ams_frontend'
              dockerfilePath: $(dockerfilePath)
              buildContext: '.'
          containerRegistry: 'dtbxprodmcacr.azurecr.io'
          nodeVersion: '20.x'
          tag: $(tag)
          injectNpmrc: true
          permissions:
            pipelines:
              accessToken: 'write'