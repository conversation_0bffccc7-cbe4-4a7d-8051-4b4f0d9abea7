'use client'
import React, { ReactNode } from 'react'
import { Box } from '@mui/material'
import { NextAppDirEmotionCacheProvider, ThemeConfig } from '@dtbx/ui/theme'
import { useAppSelector, useAppDispatch } from '@/store'
import { refreshToken } from '@dtbx/store/actions'
import {
  clearNotification,
  setDocumentToggle,
  setSidebarCollapsed,
} from '@dtbx/store/reducers'
import AppProvider from '@/store/AppProvider'
import {
  AuthWrapper,
  CustomScrollbar,
  InActivity,
  LocalNotification,
} from '@dtbx/ui/components'
import { IDView } from '@dtbx/ui/components/Overlay'
import { Sidebar } from '@dtbx/ui/components/Sidebar'
import { InternalNavBar } from '@dtbx/ui/components/Appbar'

import { sidebarConfig } from './sidebar'
import { isLoggedIn } from '@dtbx/store/utils'
import '@dtbx/ui/theme/index.css'

export default function RootLayout({
  children,
}: Readonly<{
  children: ReactNode
}>) {
  return (
    <html lang="en">
      <body>
        <AppProvider>
          <NextAppDirEmotionCacheProvider options={{ key: 'mui' }}>
            <ThemeConfig>
              <CustomScrollbar>
                <InActivity isLoggedIn={isLoggedIn}>
                  <DashboardLayout>{children}</DashboardLayout>
                </InActivity>
              </CustomScrollbar>
            </ThemeConfig>
          </NextAppDirEmotionCacheProvider>
        </AppProvider>
      </body>
    </html>
  )
}

export function DashboardLayout(props: { children: React.ReactNode }) {
  const dispatch = useAppDispatch()
  const { open } = useAppSelector((state) => state.navigation.documentToggle)
  const profile = useAppSelector((state) => state.auth.decodedToken)
  const modules = useAppSelector((state) => state.auth.channelModules)
  const { isSidebarCollapsed } = useAppSelector((state) => state.navigation)
  const notification = useAppSelector(
    (state) => state.notifications.localNotification
  )
  const accessControlSideBarConfig = sidebarConfig.filter((config) => {
    return (
      modules.filter(
        (module) =>
          module.channel === 'IAM' &&
          (module.modules?.includes(config?.module) ||
            config?.module === 'default')
      ).length > 0
    )
  })
  const notificationType =
    useAppSelector((state) => state.notifications.localNotificationType) ||
    'info'
  return (
    <AuthWrapper requiresAuth={true} isLoggedIn={isLoggedIn}>
      <Box sx={{ display: 'flex', flexDirection: 'row' }}>
        <IDView
          open={open}
          setDocumentViewer={(val) => dispatch(setDocumentToggle(val))}
        />
        <Sidebar
          sidebarConfig={accessControlSideBarConfig}
          sidebarCollapsed={(val) => dispatch(setSidebarCollapsed(val))}
        />
        <Box
          sx={{
            display: 'flex',
            flexDirection: 'column',
            width: isSidebarCollapsed ? '95vw' : 'calc(100% - 14rem)',
          }}
        >
          <InternalNavBar profile={profile} refreshToken={refreshToken} />
          <Box
            sx={{
              width: '100%',
              backgroundColor: '#FCFCFC',
            }}
          >
            <LocalNotification
              clearNotification={() => dispatch(clearNotification())}
              notification={notification}
              notificationType={notificationType}
            />
            {props.children}
          </Box>
        </Box>
      </Box>
    </AuthWrapper>
  )
}
