'use client'

import {
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  <PERSON><PERSON>,
  Typo<PERSON>,
} from '@mui/material'
import React, { useEffect, useState } from 'react'
import CheckIcon from '@mui/icons-material/Check'
import ArrowBackIcon from '@mui/icons-material/ArrowBack'
import BorderColorOutlinedIcon from '@mui/icons-material/BorderColorOutlined'
import { useCustomRouter } from '@dtbx/ui/hooks'
import { useAppDispatch, useAppSelector } from '@/store'
import { IDiffValues } from '@/store/interfaces'
import { AntTab, AntTabs, TabPanel } from '@dtbx/ui/components/Tabs'
import {
  CustomErrorChip,
  CustomSuccessChip,
  CustomWarningChip,
} from '@dtbx/ui/components/Chip'
import { LoadingButton } from '@dtbx/ui/components/Loading'

import { CheckerRequestsApiHandler } from '@/app/approval-requests/CheckerRequestsApiHandler'

import { ApprovalRequestDetails } from './ApprovalRequestDetails'
import { RoleRightsView } from './Rights'
import { RoleUsersView } from './Users'

export const RoleDetails = () => {
  const dispatch = useAppDispatch()
  const router = useCustomRouter()
  const { selectedApprovalRequest } = useAppSelector(
    (state) => state.approvalRequests
  )
  const [name, setName] = useState<string>('New')
  const { role, isLoadingUpdateRole, isLoadingDeleteRole } = useAppSelector(
    (state) => state.roles
  )
  const navigation = useAppSelector((state) => state?.navigation)
  const navigationType = navigation.switchToRoleDetails.type
  const chipStatus = () => {
    if (navigationType === 'approve') {
      return selectedApprovalRequest.status
    }
    if (navigationType === 'view') {
      return role.status ? role.status : 'APPROVED'
    }
    if (!navigationType && selectedApprovalRequest) {
      return selectedApprovalRequest.status
    }
  }
  const getName = () => {
    let name = ''

    Object.values(selectedApprovalRequest.diff).forEach(
      (value: IDiffValues) => {
        if (typeof value.newValue === 'string') {
          if (value.field === 'name') {
            name = value.newValue
          }
        }
      }
    )
    return name
  }
  //button handlers
  const handleReject = async () => {
    await CheckerRequestsApiHandler(
      selectedApprovalRequest,
      dispatch,
      router,
      `REJECT_${selectedApprovalRequest.makerCheckerType.type}`,
      'Request rejected successfully'
    )
    return router.push('/roles')
  }
  const handleApprove = async () => {
    await CheckerRequestsApiHandler(
      selectedApprovalRequest,
      dispatch,
      router,
      `ACCEPT_${selectedApprovalRequest.makerCheckerType.type}`,
      'Request accepted successfully'
    )
    return router.push('/roles')
  }
  useEffect(() => {
    const newName =
      selectedApprovalRequest?.makerCheckerType?.type !== 'ACTIVATE_GROUPS' ||
      navigationType === 'view'
        ? role.name
        : getName()
    setName(newName)
  }, [selectedApprovalRequest, role])
  //handle tabs
  const [value, setValue] = useState<number>(0)
  return (
    <Stack
      sx={{
        flexDirection: 'column',
      }}
    >
      <Stack
        sx={{
          justifyContent: 'space-between',
          flexDirection: 'row',
          px: '3%',
          marginBottom: '0.75%',
        }}
      >
        <Stack
          sx={{
            flexDirection: 'row',
            alignItems: 'center',
            alignContent: 'center',
            gap: '10px',
          }}
        >
          <Breadcrumbs>
            <Typography
              variant="body2"
              sx={{ color: 'primary.main', cursor: 'pointer' }}
              onClick={() => router.push('/roles')}
            >
              Roles
            </Typography>
            <Typography variant="body2" sx={{ color: 'primary.main' }}>
              {name}
            </Typography>
          </Breadcrumbs>
          {chipStatus() === 'PENDING' ? (
            <CustomWarningChip label={chipStatus()} />
          ) : chipStatus() === 'APPROVED' ? (
            <CustomSuccessChip label={chipStatus()} />
          ) : (
            <CustomErrorChip label={chipStatus()} />
          )}
        </Stack>
        {navigationType === 'approve' && (
          <Stack
            sx={{
              flexDirection: 'row',
              gap: '20px',
            }}
          >
            <ApprovalRequestDetails />
            {isLoadingUpdateRole || isLoadingDeleteRole ? (
              <LoadingButton />
            ) : (
              <Button
                variant="outlined"
                sx={{
                  height: '40px',
                  background: '#E3E4E4',
                  border: '1px solid #AAADB0',
                  display: 'none',
                }}
                disabled={
                  selectedApprovalRequest.status === 'REJECTED' ||
                  selectedApprovalRequest.status === 'APPROVED'
                }
                onClick={handleReject}
              >
                Reject
              </Button>
            )}
            {isLoadingUpdateRole || isLoadingDeleteRole ? (
              <LoadingButton />
            ) : (
              <Button
                variant="contained"
                endIcon={<CheckIcon />}
                sx={{
                  height: '40px',
                  display: 'none',
                }}
                disabled={
                  selectedApprovalRequest.status === 'APPROVED' ||
                  selectedApprovalRequest.status === 'REJECTED'
                }
                onClick={handleApprove}
              >
                Approve
              </Button>
            )}
          </Stack>
        )}
      </Stack>
      <Divider />
      <Stack
        sx={{
          px: '1%',
          py: '1%',
          //border: '1px solid #D0D5DD',
          background: '#FFFFFF',
          flexDirection: 'column',
          marginTop: '2vh',
          mx: '2%',
        }}
      >
        <Button
          variant="outlined"
          sx={{
            width: '10%',
            px: 0,
            height: '34px',
            border: '1px solid #AAADB0',
            boxShadow: '0px 1px 2px 0px rgba(16, 24, 40, 0.05)',
            paddingBottom: '10px',
            gap: 0,
          }}
          startIcon={<ArrowBackIcon />}
          onClick={() => router.push('/roles')}
        >
          All Roles
        </Button>
        <Stack
          sx={{
            flexDirection: 'row',
            justifyContent: 'flex-start',
            gap: '20px',
          }}
        >
          <Stack
            sx={{
              flexDirection: 'row',
              alignItems: 'flex-start',
              alignContent: 'flex-start',
              gap: '8px',
              mt: '1%',
            }}
          >
            <Typography variant="subtitle1">{name}</Typography>
            <BorderColorOutlinedIcon
              sx={{
                fontSize: '18px',
              }}
            />
          </Stack>
          <Stack>
            <AntTabs
              value={value}
              onChange={(event: React.SyntheticEvent, newValue: number) =>
                setValue(newValue)
              }
              aria-label="ant example"
              centered
              sx={{ paddingBottom: 0 }}
              variant="fullWidth"
            >
              <AntTab key={'rights'} label={'Rights'} />
              <AntTab key={'users'} label={'Users'} />
            </AntTabs>
          </Stack>
        </Stack>
        <TabPanel value={value} index={0}>
          <RoleRightsView role={role} />
        </TabPanel>
        <TabPanel value={value} index={1}>
          <RoleUsersView />
        </TabPanel>
      </Stack>
    </Stack>
  )
}
