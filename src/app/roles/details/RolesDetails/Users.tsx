import { KeyboardArrowDownRounded } from '@mui/icons-material'
import {
  Avatar,
  Button,
  Paper,
  Stack,
  styled,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableRow,
  Typography,
} from '@mui/material'
import React, { useEffect, useState } from 'react'
import { sentenceCase } from 'tiny-case'
import { IHeadCell } from '@dtbx/store/interfaces'
import { useAppDispatch, useAppSelector } from '@/store'
import { getUsersByRoleId } from '@/store/actions'
import { setDrawer } from '@dtbx/store/reducers'
import { CustomFilterBox } from '@/app/approval-requests/CustomFilterBox'
import {
  CustomPagination,
  CustomTableHeader,
  PaginationOptions,
} from '@dtbx/ui/components/Table'
import { CustomCheckBox } from '@dtbx/ui/components/CheckBox'
import { CustomerStatusChip } from '@dtbx/ui/components/Chip'

import { UserMoreMenu } from '@/app/users/users/UserMoreMenu'
import { CustomSkeleton } from '@dtbx/ui/components'
import EmptySearchResult from '@/app/users/users/EmptySearchResult'

const tableHeadItems: IHeadCell[] = [
  {
    label: 'Name',
    id: 'name',
    alignCenter: false,
    alignRight: false,
  },
  {
    label: 'Status',
    id: 'status',
    alignCenter: false,
    alignRight: false,
  },
  {
    label: 'Phone Number',
    id: 'phone',
    alignCenter: false,
    alignRight: false,
  },
  {
    label: 'Role',
    id: 'role',
    alignCenter: false,
    alignRight: false,
  },
  {
    label: 'Actions',
    id: 'actions',
    alignCenter: false,
    alignRight: false,
  },
]

type Order = 'asc' | 'desc'
const CustomTableCell = styled(TableCell)(() => ({
  color: '#667085',
}))

export const RoleUsersView = () => {
  const dispatch = useAppDispatch()
  const [selected, setSelected] = useState<readonly string[]>([])
  const [order, setOrder] = useState<Order>('asc')
  const [orderBy, setOrderBy] = useState<string>('id')
  const [page, setPage] = useState<number>(1)
  const [loading, setLoading] = useState<boolean>(true)
  const [openFilter, setOpenFilter] = React.useState<boolean>(false)
  const [searchByValue, setSearchByValue] = React.useState<string>('First name')
  const [paginationOptions, setPaginationOptions] = useState({
    page: page,
    size: 10,
    totalPages: 0,
  })
  /*************************start pagination handlers***************************/
  const handlePagination = async (newOptions: PaginationOptions) => {
    setLoading(true)
    setPaginationOptions(newOptions) // Update local pagination state
    await getUsersByRoleId(dispatch, role.id, newOptions.page, 10)
    setPage(newOptions.page)
    setLoading(false)
  }
  /*************************end pagination handlers***************************/
  const { roleUsers } = useAppSelector((state) => state.users)
  const [searchValue, setSearchValue] = React.useState<string>('')
  const handleSearch = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchValue(e.target.value)
    if (e.target.value === '') {
      setLoading(true)
      fetchRoleUsers()
    }
  }
  const { role } = useAppSelector((state) => state.roles)
  const handleSelectAll = (event: React.ChangeEvent<HTMLInputElement>) => {
    if (event.target.checked) {
      const newSelected = roleUsers?.data?.map((n: { id: string }) => n.id)
      setSelected(newSelected)
      return
    }
    setSelected([])
  }
  const handleSelectOne = (event: React.MouseEvent<unknown>, id: string) => {
    const selectedIndex = selected.indexOf(id)
    let newSelected: readonly string[] = []

    if (selectedIndex === -1) {
      newSelected = newSelected.concat(selected, id)
    } else if (selectedIndex === 0) {
      newSelected = newSelected.concat(selected.slice(1))
    } else if (selectedIndex === selected.length - 1) {
      newSelected = newSelected.concat(selected.slice(0, -1))
    } else if (selectedIndex > 0) {
      newSelected = newSelected.concat(
        selected.slice(0, selectedIndex),
        selected.slice(selectedIndex + 1)
      )
    }
    setSelected(newSelected)
  }
  const handleRequestSort = (
    event: React.MouseEvent<unknown>,
    property: string
  ) => {
    const isAsc = orderBy === property && order === 'asc'
    setOrder(isAsc ? 'desc' : 'asc')
    setOrderBy(property)
  }

  useEffect(() => {
    if (searchValue !== '') {
      filterRoleUsers()
    }
  }, [searchValue, searchByValue])
  useEffect(() => {
    role && fetchRoleUsers()
  }, [role])
  const fetchRoleUsers = async () => {
    await getUsersByRoleId(dispatch, role.id, 1, 10)
    setLoading(false)
  }
  const filterRoleUsers = async () => {
    setLoading(true)
    await getUsersByRoleId(
      dispatch,
      role.id,
      1,
      10,
      `&${searchByValue.includes('First') ? 'firstName' : 'lastName'}=${searchValue}`
    )
    setLoading(false)
  }
  return (
    <Stack
      sx={{
        display: 'flex',
        py: '1%',
        flexDirection: 'column',
        gap: '2vh',
      }}
    >
      <Stack
        sx={{
          flexDirection: 'row',
          justifyContent: 'flex-start',
          gap: '17px',
        }}
      >
        <CustomFilterBox
          openFilter={openFilter}
          setOpenFilter={setOpenFilter}
          searchValue={searchValue}
          setSearchByValue={(val) => {
            setSearchByValue(val)
          }}
          searchByValues={['First name', 'Last name']}
          handleSearch={handleSearch}
          filters={[]}
          onFilterChange={() => {}}
        />
        <Button
          variant="outlined"
          endIcon={<KeyboardArrowDownRounded />}
          sx={{
            minWidth: '161px',
            height: '44px',
            StackShadow: '0px 1px 2px 0px rgba(16, 24, 40, 0.05)',
            border: '1px solid #D0D5DD',

            background: '#E3E4E4',
          }}
        >
          Batch Edit
        </Button>
      </Stack>
      {
        <Paper
          elevation={0}
          sx={{
            boxShadow:
              '0px 1px 3px 0px rgba(16, 24, 40, 0.10), 0px 1px 2px 0px rgba(16, 24, 40, 0.06)',
            borderRadius: '4px',
            border: '1px solid #EAECF0',
            background: '#FFFFFF',
          }}
        >
          {loading ? (
            <CustomSkeleton
              animation="pulse"
              variant="rectangular"
              width={'100%'}
              height={'60vh'}
            />
          ) : roleUsers?.data.length === 0 ? (
            <EmptySearchResult />
          ) : (
            <TableContainer
              component={Paper}
              elevation={0}
              sx={{
                boxShadow: 'none',
              }}
            >
              <Table sx={{ minWidth: 650 }}>
                <CustomTableHeader
                  order={order}
                  orderBy={orderBy}
                  headLabel={tableHeadItems}
                  showCheckbox={true}
                  rowCount={roleUsers?.totalElements}
                  numSelected={selected.length}
                  onRequestSort={handleRequestSort}
                  onSelectAllClick={handleSelectAll}
                />
                <TableBody>
                  {roleUsers?.data &&
                    roleUsers?.data.map((value) => {
                      const {
                        firstName,
                        lastName,
                        roles,
                        phoneNumber,
                        email,
                        id,
                        status,
                      } = value
                      const isItemSelected = selected.indexOf(id) !== -1
                      return (
                        <TableRow
                          hover
                          key={id}
                          tabIndex={-1}
                          role="checkbox"
                          onClick={(event) => handleSelectOne(event, id)}
                          selected={isItemSelected}
                          aria-checked={isItemSelected}
                        >
                          <TableCell padding="checkbox">
                            <CustomCheckBox
                              checked={isItemSelected}
                              inputProps={{
                                'aria-labelledby': id,
                              }}
                            />
                          </TableCell>
                          <TableCell
                            sx={{
                              // padding: '10px 24px 10px 16px',
                              display: 'flex',
                              gap: '12px',
                            }}
                          >
                            <Avatar>{firstName[0] + lastName[0]}</Avatar>
                            <Stack>
                              <Typography
                                variant="body2"
                                sx={{
                                  color: '#000A12',
                                }}
                              >
                                {firstName + ' ' + lastName}
                              </Typography>
                              <Typography
                                variant="body2"
                                sx={{
                                  fontWeight: 400,
                                }}
                              >
                                {email}
                              </Typography>
                            </Stack>
                          </TableCell>
                          <TableCell>
                            {/* {status === 'ACTIVE' ? (
                              <CustomActiveChip label={sentenceCase(status)} />
                            ) : status === 'INACTIVE' ? (
                              <CustomErrorChip label={sentenceCase(status)} />
                            ) : status === 'PENDING' ? (
                              <CustomWarningChip label={sentenceCase(status)} />
                            ) : null} */}

                            <CustomerStatusChip label={status} />
                          </TableCell>
                          <TableCell>
                            <Typography variant="body2">
                              {phoneNumber}
                            </Typography>
                          </TableCell>
                          <TableCell
                            sx={{
                              cursor: 'pointer',
                            }}
                          >
                            {/* <ViewUserRole role={roles[0]} /> */}
                            <Typography
                              sx={{
                                color: '#000A12',
                                fontWeight: 400,
                                textDecoration: 'underline',
                                fontSize: '14px',
                                lineHeight: '20px',
                              }}
                              onClick={() => {
                                dispatch(
                                  setDrawer({
                                    open: true,
                                    drawerChildren: {
                                      childType: 'view_right',
                                      data: roles[0] && roles[0],
                                    },
                                    header: sentenceCase(
                                      roles[0] && roles[0].name
                                    ),
                                  })
                                )
                              }}
                            >
                              {roles[0] && roles[0].name}
                            </Typography>
                          </TableCell>
                          <CustomTableCell>
                            <UserMoreMenu user={value} />
                          </CustomTableCell>
                        </TableRow>
                      )
                    })}
                </TableBody>
              </Table>
            </TableContainer>
          )}
          {roleUsers?.totalNumberOfPages > 0 && !loading && (
            <CustomPagination
              options={{
                ...paginationOptions,
                totalPages: roleUsers?.totalNumberOfPages,
              }}
              handlePagination={handlePagination}
            />
          )}
        </Paper>
      }
    </Stack>
  )
}
