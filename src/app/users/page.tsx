'use client'
import { Chip, Divider, Stack, Typography } from '@mui/material'
import React from 'react'
import { useAppSelector } from '@/store'
import { AntTab, AntTabs, TabPanel } from '@dtbx/ui/components/Tabs'
import UserPage from './users/page'

const StaffUsersPage: React.FC = () => {
  const [value, setValue] = React.useState<number>(0)
  const handleChange = (event: React.SyntheticEvent, newValue: number) => {
    setValue(newValue)
  }
  const userCount = useAppSelector(
    (state) => state.users.usersResponse?.totalElements
  )
  return (
    <Stack>
      <>
        <AntTabs
          value={value}
          onChange={handleChange}
          aria-label="ant example"
          sx={{
            marginLeft: '2%',
          }}
        >
          <AntTab
            label={
              <Stack
                sx={{
                  display: 'flex',
                  flexDirection: 'row',
                  alignItems: 'center',
                  gap: '8px',
                }}
              >
                <Typography>Users</Typography>
                <Chip
                  label={userCount}
                  sx={{
                    minWidth: '39px',
                    height: '22px',
                    border: '1px solid #EAECF0',
                    backgroundColor: '#F9FAFC',
                    color: '#2A3339',
                    fontSize: '12px',
                    fontWeight: '500',
                    lineHeight: '18px',
                  }}
                />
              </Stack>
            }
            key="users"
          />
        </AntTabs>
        <Divider />
        <TabPanel value={value} index={0}>
          <UserPage />
        </TabPanel>
      </>
    </Stack>
  )
}

export default StaffUsersPage
