'use client'

import {
  Avatar,
  Box,
  Paper,
  styled,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableRow,
  Typography,
} from '@mui/material'
import React, { useState } from 'react'
import { useAppDispatch, useAppSelector } from '@/store'
import { getUsers } from '@/store/actions'
import { CustomSkeleton } from '@dtbx/ui/components/Loading'
import {
  CustomPagination,
  CustomTableHeader,
  PaginationOptions,
} from '@dtbx/ui/components/Table'
import { CustomChip, CustomerStatusChip } from '@dtbx/ui/components/Chip'

import { UserMoreMenu } from '@/app/users/users/UserMoreMenu'

import EmptySearchResult from './EmptySearchResult'
import { ViewRoles } from '@/app/rights/RightsDialogs'
import { formatTimestamp } from '@dtbx/store/utils'

type Order = 'asc' | 'desc'
export const CustomTableCell = styled(TableCell)(() => ({
  color: '#667085',
}))

const header = [
  { id: 'name', label: 'Name', alignRight: false },
  { id: 'status', label: 'Status', alignRight: false },
  { id: 'phoneNumber', label: 'Phone Number', alignRight: false },
  { id: 'role', label: 'Role', alignRight: false },
  { id: 'dateCreated', label: 'Date Created', alignRight: false },
  { id: 'lastLoginDate', label: 'Last Login', alignRight: false },
  { id: '', label: 'Actions', alignRight: false },
]
interface UsersListProps {
  onSelectedCountChange: (count: number) => void
  onExport: (selectedIds: string[]) => void
  searchByField: string
}

export const UsersList: React.FC<UsersListProps> = ({
  onSelectedCountChange,
  onExport,
  searchByField,
}) => {
  const dispatch = useAppDispatch()
  const { usersResponse, searchUserValue, userFilterValue, isLoadingUsers } =
    useAppSelector((state) => state.users)
  const [selected, setSelected] = useState<readonly string[]>([])
  const [order, setOrder] = useState<Order>('asc')
  const [orderBy, setOrderBy] = useState<string>('id')
  const [paginationOptions, setPaginationOptions] = useState({
    page: usersResponse.pageNumber,
    size: 10,
    totalPages: usersResponse.totalNumberOfPages,
  })

  /*************************start pagination handlers***************************/
const handlePagination = async (newOptions: PaginationOptions) => {
  setPaginationOptions(newOptions)

  const params: any = {
    page: newOptions.page,
    size: newOptions.size,
    ...userFilterValue,
  }
  if (searchUserValue) {
    params[searchByField] = searchUserValue
  }

  await getUsers(dispatch, params)
}
  /*************************end pagination handlers**************************/

  const handleSelectAll = (event: React.ChangeEvent<HTMLInputElement>) => {
    if (event.target.checked) {
      const newSelected = usersResponse?.data.map((n: { id: string }) => n.id)
      setSelected(newSelected)
      onSelectedCountChange(newSelected.length)
      onExport(newSelected)
      return
    }
    setSelected([])
    onSelectedCountChange(0)
    onExport([])
  }
  const handleSelectOne = (event: React.MouseEvent<unknown>, id: string) => {
    const selectedIndex = selected.indexOf(id)
    let newSelected: readonly string[] = []

    if (selectedIndex === -1) {
      newSelected = newSelected.concat(selected, id)
    } else if (selectedIndex === 0) {
      newSelected = newSelected.concat(selected.slice(1))
    } else if (selectedIndex === selected.length - 1) {
      newSelected = newSelected.concat(selected.slice(0, -1))
    } else if (selectedIndex > 0) {
      newSelected = newSelected.concat(
        selected.slice(0, selectedIndex),
        selected.slice(selectedIndex + 1)
      )
    }
    setSelected(newSelected)
    onSelectedCountChange(newSelected.length)
    onExport([...newSelected])
  }
  const handleRequestSort = (
    event: React.MouseEvent<unknown>,
    property: string
  ) => {
    const isAsc = orderBy === property && order === 'asc'
    setOrder(isAsc ? 'desc' : 'asc')
    setOrderBy(property)
  }
  return (
    <>
      {isLoadingUsers ? (
        <CustomSkeleton
          animation="pulse"
          variant="rectangular"
          width={'100%'}
          height={'60vh'}
        />
      ) : (searchUserValue.length > 0 ||
          Object.keys(userFilterValue).length > 0) &&
        usersResponse?.data.length === 0 ? (
        <EmptySearchResult />
      ) : (
        <Paper
          sx={{
            width: '100%',
            overflow: 'hidden',
            borderRadius: '4px',
            border: '1px solid #EAECF0',
            background: '#FEFEFE',
            boxShadow:
              '0px 1px 2px 0px rgba(16, 24, 40, 0.06), 0px 1px 3px 0px rgba(16, 24, 40, 0.10)',
          }}
        >
          <TableContainer
            component={Paper}
            sx={{
              boxShadow: 'none',
            }}
          >
            <Table
              sx={{ minWidth: 650 }}
              aria-label="designations table"
              size="small"
            >
              <CustomTableHeader
                order={order}
                orderBy={orderBy}
                headLabel={header}
                showCheckbox={false}
                rowCount={usersResponse.data.length}
                numSelected={selected.length}
                onRequestSort={handleRequestSort}
                onSelectAllClick={handleSelectAll}
              />
              <TableBody>
                {usersResponse.data &&
                  usersResponse.data.map((row) => {
                    const {
                      id,
                      firstName,
                      lastName,
                      roles,
                      email,
                      phoneNumber,
                      status,
                      lastLoginDate,
                      dateCreated,
                    } = row
                    const isItemSelected = selected.indexOf(id) !== -1
                    return (
                      <TableRow
                        hover
                        key={id}
                        tabIndex={-1}
                        role="checkbox"
                        onClick={(event) => handleSelectOne(event, row.id)}
                        selected={isItemSelected}
                        aria-checked={isItemSelected}
                      >
                        <TableCell
                          sx={{
                            padding: '10px 24px 10px 16px',
                            display: 'flex',
                            gap: '12px',
                          }}
                        >
                          <Avatar>
                            {firstName && lastName
                              ? `${firstName[0] + lastName[0]}`
                              : 'N/A'}
                          </Avatar>
                          <Box>
                            <Typography
                              variant="body2"
                              sx={{
                                color: '#000A12',
                              }}
                            >
                              {firstName && lastName
                                ? `${firstName} ${lastName}`
                                : 'N/A'}
                            </Typography>
                            <Typography
                              variant="body2"
                              sx={{
                                fontWeight: 400,
                              }}
                            >
                              {email}
                            </Typography>
                          </Box>
                        </TableCell>
                        <TableCell>
                          <CustomerStatusChip label={status} />
                        </TableCell>
                        <TableCell sx={{ textWrap: 'nowrap' }}>
                          {phoneNumber || 'N/A'}
                        </TableCell>
                        <TableCell
                          sx={{
                            cursor: 'pointer',
                          }}
                        >
                          <Box
                            sx={{
                              display: 'flex',
                              flexDirection: 'row',
                              gap: '4px',
                            }}
                          >
                            {roles &&
                              roles
                                .slice(0, 1)
                                .map((role, index) => (
                                  <CustomChip
                                    key={role.id + index}
                                    label={role.name}
                                  />
                                ))}
                            {roles && roles.length > 1 && (
                              <ViewRoles
                                roles={[...roles]}
                                permission={`${firstName}`}
                              />
                            )}
                          </Box>
                        </TableCell>
                        <TableCell sx={{ textWrap: 'nowrap' }}>
                          {dateCreated ? formatTimestamp(dateCreated) : 'N/A'}
                        </TableCell>
                        <TableCell sx={{ textWrap: 'nowrap' }}>
                          {lastLoginDate
                            ? formatTimestamp(lastLoginDate)
                            : 'N/A'}
                        </TableCell>
                        <CustomTableCell>
                          <UserMoreMenu user={row} />
                        </CustomTableCell>
                      </TableRow>
                    )
                  })}
              </TableBody>
            </Table>
          </TableContainer>
          {usersResponse.totalNumberOfPages > 0 && (
            <CustomPagination
              options={{
                ...paginationOptions,
                totalPages: usersResponse.totalNumberOfPages,
              }}
              handlePagination={handlePagination}
            />
          )}
        </Paper>
      )}
    </>
  )
}
