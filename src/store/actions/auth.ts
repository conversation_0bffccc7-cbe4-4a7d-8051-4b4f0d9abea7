import { Dispatch } from '@reduxjs/toolkit'
import { AppRouterInstance } from 'next/dist/shared/lib/app-router-context.shared-runtime'
import { secureapi } from '@dtbx/store/utils'
import {
  clearCustomerState,
  resetRolesStore,
  resetUsersStore,
} from '../reducers'
import { clearNotification } from "@dtbx/store/reducers"

export const handleLogout = async (
  dispatch: Dispatch,
  router: AppRouterInstance
) => {
  try {
    await secureapi.post('/backoffice-auth/users/logout')
    dispatch(resetUsersStore())
    dispatch(resetRolesStore())
    dispatch(clearNotification())
    dispatch(clearCustomerState())
    window.localStorage.clear()
    router.push('/')
  } catch (e) {
    console.error('ERROR ON LOGOUT', e)
  }
}