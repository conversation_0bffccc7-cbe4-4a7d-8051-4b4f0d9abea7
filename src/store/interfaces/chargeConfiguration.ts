export type Status = 'Active' | 'Inactive' | 'Pending Approval'

export interface ITariffData {
  id: string
  name: string
  status: Status
  createdOn: string
  createdBy: string
  dateCreated?: string
  actions?: string
  // Add other relevant fields for tariff configuration
}

export interface ITariffCreate {
  id: string
  name: string
  status: Status
  createdOn: string
  createdBy: string
  dateCreated?: string
  actions?: string
}

export interface TariffDataResponse {
  pageNumber: number
  pageSize: number
  totalNumberOfPages: number
  totalElements: number
  data: ITariffData[]
}
export interface ErrorResponse {
  status: number
  message: string
  error: string
}
export interface IConfigurationData {
  id: string
  name: string
  status: Status
  type: string
  dailyFrequencyLimit: string
  dateCreated?: string
  actions?: string
  // Add other relevant fields for tariff configuration
}
export interface IConfigurationCreate {
  id: string
  name: string
  status: Status
  type: string
  dailyFrequencyLimit: string
  dateCreated?: string
  actions?: string
  // Add other relevant fields for tariff configuration
}
export interface ConfigurationResponse {
  pageNumber: number
  pageSize: number
  totalNumberOfPages: number
  totalElements: number
  data: IConfigurationData[]
}

export interface IConfiguredService {
  // serviceName: string
  id: string
  name: string
}

export interface IConfigurationLogs {
  id: string
  name: string
  maker: string
  checker: string
  actions?: string
  status?: string
}

export interface IConfigurationComparisonData {
  id: string
  field: string
  oldValue: number
  newValue: number
}
