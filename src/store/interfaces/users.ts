import { IRole } from './roles'

export type FileFormat = 'excel' | 'csv' | 'json' | 'pdf'


export interface IUser {
  id: string
  firstName: string
  lastName: string
  middleName: string
  roles: IRole[]
  email: string
  phoneNumber: string
  dateCreated?: string
  status: string
  country?: string
  lastLoginDate?: string
  [key: string]: string | number | boolean | IRole[] | undefined
}
export interface IUsersResponse {
  pageNumber: number
  pageSize: number
  totalNumberOfPages: number
  totalElements: number
  data: IUser[]
}
export interface IDecodeToken {
  last_name: string
  first_name: string
  user_id: string
  authorities: string[]
  sub: string
  iat: number
  exp: number
  resources?: IResource[]
}
export interface IResource {
  resourceType: string
  resourceIds: string[]
}
export interface ICreateUser {
  firstName: string
  lastName: string
  middleName?: string
  roleIds: string[]
  email: string
  phoneNumber: string
  comments?: string
}
export interface IUpdateUser {
  roleIds: string[]
  comments: string
}
export interface ICheckUser {
  comments: string
}
export interface IADUserProfile {
  mail: string
  mobilePhone: string
  surname: string
  givenName: string
}

//loans interfaces

export interface ILoanProduct {
  id: string
  dateCreated: string
  dateModified: string
  createdBy: string
  modifiedBy: string
  code: string
  name: string
  country: string
  currency: string
  organization: IProductOrganization
  type: IProductType
  expiryDate: string
  exposureLimit: number
  status: string
  customerType: string
  minimumAmount: number
  maximumAmount: number
  measureOfTenure: string
  minimumTenure: number
  maximumTenure: number
  interestRateType: string
  dynamicInterestFixedComponent: number
  interestRate: number
  facilityFee: number
  exciseDuty: number
  facilityFeeRecoveryType: string
  interestRecoveryType: string
  minimumInterestRecoveryType: string
  upfrontInterestRecognitionType: string
  minimumInterestCalculationMode: string
  externalProductName: string
  rollOverFee: number
  rollOverPeriod: number
  maxRollOverCount: number
  prepaymentType: string
  prepaymentCalculation: string
  prepaymentValue: number
  penalty: number
  interestCalculation: string
  amortizationMode: string
  multipleDrawDown: boolean
  tranches: boolean
  trancheInterval: string
  repaymentCycle: string
  numberOfInstallments: number
  earlyPaymentsAllowed: boolean
  periodInArrears: number
  minimumInterestValue: number
  interestGl: string
  facilityFeeGl: string
  exciseDutyGl: string
  rollOverGl: string
  penaltyGl: string
  prepaymentGl: string
  disbursementGl: string
  upfrontInterestLiabilityGl: string
  disbursementCreditAccountType: string
  repaymentGl: string
  gracePeriodType: string
  hasRecoveryTracking: boolean
  externalProductCode: string
  disbursementCreditAccount: string
  disbursementCreditAccountBranch: string
  isManaged: boolean
  manualApprovalAmount: number
  typeId?: string
  organizationId?: string
  loanCreationBranch?: string
}
export interface IProductOrganization {
  id: string
  dateCreated: string
  dateModified: string
  createdBy: string
  modifiedBy: string
  name: string
  cbsIdentifier: string
  limit: string
}
export interface IProductType {
  id: string
  dateCreated: string
  dateModified: string
  createdBy: string
  modifiedBy: string
  code: string
  name: string
}

export interface ILoanRequestsSummary {
  pageNumber: number
  pageSize: number
  totalElements: number
  totalNumberOfPages: number
}

export interface IPlatformResponse {
  status: string
  message: string
  data: IPlatform[]
}

export interface IPlatform {
  id: string
  name: string
}