import { createSlice, PayloadAction } from '@reduxjs/toolkit'

import {
  IPermission,
  IPermissionGroup,
  IPermissionGroupResponse,
  IPermissionResponse,
  IRole,
  IRoleResponse,
} from '../interfaces'

export interface RolesReducerProps {
  rolesList: IRole[]
  tableListRoles: IRole[]
  isLoadingTableListRoles: boolean
  role: IRole
  isLoadingRole: boolean
  isGetRoleSuccess: boolean
  isGetRoleError: boolean
  permissions: IPermission[]
  isLoadingRoles: boolean
  isLoadingCreateRole: boolean
  isLoadingUpdateRole: boolean
  isLoadingDeleteRole: boolean
  isLoadingError: boolean
  isCreateRoleSuccess: boolean
  isUpdateRoleSuccess: boolean
  isDeleteRoleSuccess: boolean
  isCreateRoleOpen: boolean
  isUpdateRoleOpen: boolean
  pageCount: number
  rolesCount: number
  permissionGroup: IPermissionGroupResponse
  isPermissionGroupLoading: boolean
  isPermissionGroupsSuccess: boolean
  permissionPageCount: number
  permissionsCount: number
  isLoadingPermissions: boolean
  roleSearchValue: string
  roleFilterValue: Record<string, string | string[]>
  permissionSearchValue: string
  permissionFilterValue: Record<string, string | string[]>
  isCreateRoleFailed: boolean
  isUpdateRoleFailed: boolean
  //Role reports
  isGeneratedRoleReportLoading: boolean
  isGeneratedRoleReportSuccess: boolean
  isGeneratedRoleReportFailure: boolean
}

const initialState: RolesReducerProps = {
  rolesList: [],
  tableListRoles: [],
  isLoadingTableListRoles: false,
  role: {
    id: '',
    name: '',
    description: '',
    creationDate: '',
    custom: false,
    permissions: [],
    permissionsGroup: [],
  },
  isLoadingRole: false,
  isGetRoleSuccess: false,
  isGetRoleError: false,
  isLoadingRoles: false,
  isLoadingCreateRole: false,
  isLoadingUpdateRole: false,
  isLoadingDeleteRole: false,
  isLoadingError: false,
  isCreateRoleSuccess: false,
  isCreateRoleFailed: false,
  isUpdateRoleSuccess: false,
  isUpdateRoleFailed: false,
  isDeleteRoleSuccess: false,
  isCreateRoleOpen: false,
  isUpdateRoleOpen: false,
  pageCount: 0,
  rolesCount: 0,
  permissions: [],
  permissionGroup: {
    pageNumber: 0,
    pageSize: 0,
    totalNumberOfPages: 0,
    totalElements: 0,
    data: [],
  },
  isPermissionGroupLoading: false,
  isPermissionGroupsSuccess: false,
  permissionsCount: 0,
  permissionPageCount: 0,
  isLoadingPermissions: false,
  roleSearchValue: '',
  roleFilterValue: {},
  permissionSearchValue: '',
  permissionFilterValue: {},
  //Role reports
  isGeneratedRoleReportLoading: false,
  isGeneratedRoleReportSuccess: false,
  isGeneratedRoleReportFailure: false,
}

const rolesSlice = createSlice({
  name: 'roles',
  initialState,

  reducers: {
    setRoles: (state, action: PayloadAction<IRole[]>) => {
      state.rolesList = action.payload
      state.pageCount = Math.ceil(action.payload.length / 10)
      state.rolesCount = action.payload.length
    },
    setRolesWithPermissions: (state, action: PayloadAction<IRoleResponse>) => {
      state.tableListRoles = action.payload.data
      state.pageCount = action.payload.totalNumberOfPages
      state.rolesCount = action.payload.totalElements
    },
    setIsLoadingTableListRoles: (state, action: PayloadAction<boolean>) => {
      state.isLoadingTableListRoles = action.payload
    },
    setRole: (state, action: PayloadAction<IRole>) => {
      state.role = action.payload
    },
    setIsLoadingRole: (state, action: PayloadAction<boolean>) => {
      state.isLoadingRole = action.payload
    },
    setIsGetRoleSuccess: (state, action: PayloadAction<boolean>) => {
      state.isGetRoleSuccess = action.payload
    },
    setIsGetRoleError: (state, action: PayloadAction<boolean>) => {
      state.isGetRoleError = action.payload
    },
    setLoadingRoles: (state, action: PayloadAction<boolean>) => {
      state.isLoadingRoles = action.payload
    },
    setLoadingCreateRole: (state, action: PayloadAction<boolean>) => {
      state.isLoadingCreateRole = action.payload
    },
    setLoadingUpdateRole: (state, action: PayloadAction<boolean>) => {
      state.isLoadingUpdateRole = action.payload
    },
    setLoadingDeleteRole: (state, action: PayloadAction<boolean>) => {
      state.isLoadingDeleteRole = action.payload
    },
    setLoadingError: (state, action: PayloadAction<boolean>) => {
      state.isLoadingError = action.payload
    },
    setCreateRoleSuccess: (state, action: PayloadAction<boolean>) => {
      state.isCreateRoleSuccess = action.payload
    },
    setUpdateRoleSuccess: (state, action: PayloadAction<boolean>) => {
      state.isUpdateRoleSuccess = action.payload
    },
    setDeleteRoleSuccess: (state, action: PayloadAction<boolean>) => {
      state.isDeleteRoleSuccess = action.payload
    },
    setCreatingRole: (state, action: PayloadAction<boolean>) => {
      state.isCreateRoleOpen = action.payload
    },
    setUpdatingRole: (state, action: PayloadAction<boolean>) => {
      state.isUpdateRoleOpen = action.payload
    },
    setPermissions: (state, action: PayloadAction<IPermission[]>) => {
      state.permissions = action.payload
      state.permissionPageCount = Math.ceil(action.payload.length / 10)
      state.permissionsCount = action.payload.length
    },
    setPermissionsWithFilters: (
      state,
      action: PayloadAction<IPermissionResponse>
    ) => {
      state.permissions = action.payload.data
      state.permissionPageCount = action.payload.totalNumberOfPages
      state.permissionsCount = action.payload.totalElements
    },
    setIsLoadingPermissions: (state, action: PayloadAction<boolean>) => {
      state.isLoadingPermissions = action.payload
    },
    setIsPermissionGroupLoading: (state, action: PayloadAction<boolean>) => {
      state.isPermissionGroupLoading = action.payload
    },
    setIsPermissionGroupSuccess: (state, action: PayloadAction<boolean>) => {
      state.isPermissionGroupsSuccess = action.payload
    },
    setPermissionGroup: (
      state,
      action: PayloadAction<IPermissionGroupResponse>
    ) => {
      state.permissionGroup = action.payload
    },
    setRoleSearchValue: (state, action: PayloadAction<string>) => {
      state.roleSearchValue = action.payload
    },
    setPermissionSearchValue: (state, action: PayloadAction<string>) => {
      state.permissionSearchValue = action.payload
    },
    setPermissionFilterValue: (
      state,
      action: PayloadAction<Record<string, string | string[]>>
    ) => {
      state.permissionFilterValue = action.payload
    },
    setRoleFilterValue: (
      state,
      action: PayloadAction<Record<string, string | string[]>>
    ) => {
      state.roleFilterValue = action.payload
    },
    setCreateRoleFailed: (state, action: PayloadAction<boolean>) => {
      state.isCreateRoleFailed = action.payload
    },
    setUpdateRoleFailed: (state, action: PayloadAction<boolean>) => {
      state.isUpdateRoleFailed = action.payload
    },
    setGeneratedRoleReportLoading: (state, action: PayloadAction<boolean>) => {
      state.isGeneratedRoleReportLoading = action.payload
    },
    setGeneratedRoleReportSuccess: (state, action: PayloadAction<boolean>) => {
      state.isGeneratedRoleReportLoading = action.payload
    },
    setGeneratedRoleReportFailure: (state, action: PayloadAction<boolean>) => {
      state.isGeneratedRoleReportLoading = action.payload
    },
    resetRolesStore: () => initialState,
  },
})
export const {
  setRoles,
  setRolesWithPermissions,
  setIsLoadingTableListRoles,
  setUpdatingRole,
  setCreatingRole,
  setLoadingRoles,
  setLoadingCreateRole,
  setLoadingUpdateRole,
  setLoadingDeleteRole,
  setLoadingError,
  setCreateRoleSuccess,
  setUpdateRoleSuccess,
  setDeleteRoleSuccess,
  setPermissions,
  setPermissionsWithFilters,
  setIsLoadingPermissions,
  setIsPermissionGroupLoading,
  setIsPermissionGroupSuccess,
  setPermissionGroup,
  resetRolesStore,
  setRoleFilterValue,
  setRoleSearchValue,
  setPermissionSearchValue,
  setPermissionFilterValue,
  setRole,
  setIsLoadingRole,
  setIsGetRoleSuccess,
  setIsGetRoleError,
  setCreateRoleFailed,
  setUpdateRoleFailed,
  //Role reports
  setGeneratedRoleReportLoading,
  setGeneratedRoleReportSuccess,
  setGeneratedRoleReportFailure,
} = rolesSlice.actions
export default rolesSlice.reducer
