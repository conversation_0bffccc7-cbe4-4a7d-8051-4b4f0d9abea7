{
  "extends": "@dtbx/typescript-config/nextjs.json",
  "compilerOptions": {
    "baseUrl": "./",
    "plugins": [
      {
        "name": "next"
      }
    ],
    "paths": {
      "@/*": [
        "./src/*"
      ],
      "@public/*": [
        "./public/*"
      ]
    },
    "typeRoots": [
      "./node_modules/@dtbx/ui/dist/types.d.ts"
    ]
  },
  "include": [
    "next-env.d.ts",
    "next.config.ts",
    "**/*.ts",
    "**/*.tsx",
    ".next/types/**/*.ts",
  ],
  "exclude": [
    "node_modules"
  ]
}
