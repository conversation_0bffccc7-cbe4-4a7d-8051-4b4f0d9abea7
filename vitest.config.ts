import { nextConfig } from '@dtbx/vitest-config/next'
import { defineConfig, mergeConfig, ViteUserConfig } from 'vitest/config'

// Custom config to handle ES module issues
const customConfig: ViteUserConfig = defineConfig({
  test: {
    // Transform @dtbx/ui package to handle ES modules
    testTransformMode: {
      ssr: ['/node_modules/@dtbx/ui'],
    },
    // Handle dependencies that need to be processed
    server: {
      deps: {
        inline: [
          '@dtbx/ui',
          '@dtbx/store',
          '@mui/material',
          '@emotion/react',
          '@emotion/styled',
        ],
      },
    },
  },
})

export default mergeConfig(nextConfig, customConfig)
 